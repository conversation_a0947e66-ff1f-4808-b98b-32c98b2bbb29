<?php
session_start();
include '../../../config.php';
error_reporting(E_ALL);
ini_set('display_errors', 0); // Jangan tampilkan error di output JSON

// Cek session
if(empty($_SESSION['username'])){
    die(json_encode(['error' => 'Session expired']));
}

// Cek koneksi database
if (!$koneksi) {
    die(json_encode(['error' => 'Database connection failed']));
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Fungsi untuk format tanggal Indonesia
function tgl($tanggal){
    if(empty($tanggal) || $tanggal == '0000-00-00') return '-';
    $bulan = array(
        1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    );
    $split = explode('-', $tanggal);
    return (int)$split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}

// Fungsi untuk escape string SQL
function escapeString($koneksi, $string) {
    return mysqli_real_escape_string($koneksi, $string);
}

// ===== KONFIGURASI DATATABLES =====
$table = 'edoc_dokumentasi';
$primaryKey = 'ID';

// Kolom untuk ordering
$orderColumns = array(
    0 => 'ed.ID',
    1 => 'ed.TANGGAL',
    2 => 'ed.JUDUL',
    3 => 'er1.DESKRIPSI',
    4 => 'ed.LOKASI',
    5 => 'u.UNIT',
    6 => 'er2.DESKRIPSI',
    7 => 'ed.ID'
);

// ===== PARAMETER DATATABLES =====
$draw = intval($_POST['draw']);
$start = intval($_POST['start']);
$length = intval($_POST['length']);
$searchValue = escapeString($koneksi, $_POST['search']['value']);

// ===== KONDISI HAK AKSES =====
$whereAccess = "";
if ($hak_akses == '2') {
    // User unit hanya bisa melihat data unitnya sendiri
    $whereAccess = " AND ed.UNIT = '$user'";
}

// ===== KONDISI PENCARIAN =====
$searchClause = "";
if (!empty($searchValue)) {
    $searchClause = " AND (
        ed.JUDUL LIKE '%$searchValue%' OR
        ed.LOKASI LIKE '%$searchValue%' OR
        ed.KETERANGAN LIKE '%$searchValue%' OR
        er1.DESKRIPSI LIKE '%$searchValue%' OR
        er2.DESKRIPSI LIKE '%$searchValue%' OR
        u.UNIT LIKE '%$searchValue%' OR
        DATE_FORMAT(ed.TANGGAL, '%d/%m/%Y') LIKE '%$searchValue%'
    )";
}

// ===== KONDISI ORDER =====
$orderCol = isset($orderColumns[$_POST['order'][0]['column']]) ? $orderColumns[$_POST['order'][0]['column']] : 'ed.TANGGAL';
$orderDir = in_array(strtoupper($_POST['order'][0]['dir']), ['ASC', 'DESC']) ? $_POST['order'][0]['dir'] : 'DESC';

// ===== QUERY UTAMA =====
$sql = "
SELECT
    SQL_CALC_FOUND_ROWS
    ed.ID, ed.JUDUL, ed.TANGGAL, ed.LOKASI, ed.KETERANGAN,
    er1.DESKRIPSI as KATEGORI,
    er2.DESKRIPSI as JENIS_DOK,
    u.UNIT
FROM edoc_dokumentasi ed
LEFT JOIN edoc_refrensi er1 ON ed.ID_KATEGORI = er1.ID
LEFT JOIN edoc_refrensi er2 ON ed.ID_JENIS_DOK = er2.ID
LEFT JOIN unit u ON ed.UNIT = u.ID
WHERE ed.STATUS = 1 $whereAccess $searchClause
ORDER BY $orderCol $orderDir
LIMIT $start, $length
";

$result = mysqli_query($koneksi, $sql);
if (!$result) {
    die(json_encode(['error' => 'Query Error: ' . mysqli_error($koneksi)]));
}

// Ambil total filtered records
$totalFilteredQuery = mysqli_query($koneksi, "SELECT FOUND_ROWS() as total");
$totalFiltered = mysqli_fetch_array($totalFilteredQuery)['total'];

// Ambil total records
$totalQuery = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM edoc_dokumentasi WHERE STATUS = 1 $whereAccess");
$totalRecords = mysqli_fetch_array($totalQuery)['total'];

// ===== BUAT DATA OUTPUT =====
$data = array();
$no = $start + 1;

while ($row = mysqli_fetch_array($result)) {
    $nestedData = array();
    
    // No
    $nestedData[] = $no;
    
    // Tanggal
    $nestedData[] = tgl($row['TANGGAL']);
    
    // Judul
    $nestedData[] = htmlspecialchars($row['JUDUL']);
    
    // Kategori
    $nestedData[] = htmlspecialchars($row['KATEGORI']);
    
    // Lokasi
    $nestedData[] = htmlspecialchars($row['LOKASI']);
    
    // Unit
    $nestedData[] = htmlspecialchars($row['UNIT']);
    
    // Jenis Dokumentasi
    $nestedData[] = htmlspecialchars($row['JENIS_DOK']);
    
    // Aksi
    $aksi = "
        <div class='btn-group'>
            <button type='button' class='btn btn-info btn-sm' onclick='detailDokumentasi(".$row['ID'].")' title='Detail'>
                <i class='fa fa-eye'></i>
            </button>
            <button type='button' class='btn btn-warning btn-sm' onclick='editDokumentasi(".$row['ID'].")' title='Edit'>
                <i class='fa fa-edit'></i>
            </button>
            <button type='button' class='btn btn-danger btn-sm' onclick='hapusDokumentasi(".$row['ID'].")' title='Hapus'>
                <i class='fa fa-trash'></i>
            </button>
        </div>
    ";
    $nestedData[] = $aksi;
    
    $data[] = $nestedData;
    $no++;
}

// ===== OUTPUT JSON =====
$json_data = array(
    "draw" => $draw,
    "recordsTotal" => intval($totalRecords),
    "recordsFiltered" => intval($totalFiltered),
    "data" => $data
);

echo json_encode($json_data);
?>

@-webkit-keyframes ball-scale-multiple
{
    0%
    {
        -webkit-transform: scale(0);
                transform: scale(0);

        opacity: 0;
    }
    5%
    {
        opacity: 1;
    }
    100%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: 0;
    }
}

@-moz-keyframes ball-scale-multiple
{
    0%
    {
        -moz-transform: scale(0);
             transform: scale(0);

        opacity: 0;
    }
    5%
    {
        opacity: 1;
    }
    100%
    {
        -moz-transform: scale(1);
             transform: scale(1);

        opacity: 0;
    }
}

@-o-keyframes ball-scale-multiple
{
    0%
    {
        -o-transform: scale(0);
           transform: scale(0);

        opacity: 0;
    }
    5%
    {
        opacity: 1;
    }
    100%
    {
        -o-transform: scale(1);
           transform: scale(1);

        opacity: 0;
    }
}

@keyframes ball-scale-multiple
{
    0%
    {
        -webkit-transform: scale(0);
           -moz-transform: scale(0);
             -o-transform: scale(0);
                transform: scale(0);

        opacity: 0;
    }
    5%
    {
        opacity: 1;
    }
    100%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);

        opacity: 0;
    }
}

.ball-scale-multiple
{
    position: relative;
}
.ball-scale-multiple > div:nth-child(2)
{
    -webkit-animation-delay: -.4s;
       -moz-animation-delay: -.4s;
         -o-animation-delay: -.4s;
            animation-delay: -.4s;
}
.ball-scale-multiple > div:nth-child(3)
{
    -webkit-animation-delay: -.2s;
       -moz-animation-delay: -.2s;
         -o-animation-delay: -.2s;
            animation-delay: -.2s;
}
.ball-scale-multiple > div
{
    position: absolute;
    top: 0;

    width: 15px;
    width: 60px;
    height: 15px;
    height: 60px;
    margin: 2px;
    margin: 0;

    -webkit-animation: ball-scale-multiple 1s 0s linear infinite;
       -moz-animation: ball-scale-multiple 1s 0s linear infinite;
         -o-animation: ball-scale-multiple 1s 0s linear infinite;
            animation: ball-scale-multiple 1s 0s linear infinite; 

    opacity: 0;
    border-radius: 100%;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

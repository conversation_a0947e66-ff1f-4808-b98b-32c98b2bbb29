<?php
include '../../config.php';

echo "<h2>Instalasi Tabel Dokumentasi Kegiatan</h2>";

// SQL untuk membuat tabel
$queries = [
    // Tabel edoc_dokumentasi
    "CREATE TABLE IF NOT EXISTS `edoc_dokumentasi` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `JUDUL` varchar(255) NOT NULL,
      `ID_KATEGORI` int(11) NOT NULL,
      `TANGGAL` date NOT NULL,
      `LOKASI` varchar(255) NOT NULL,
      `UNIT` varchar(10) NOT NULL,
      `ID_JENIS_DOK` int(11) NOT NULL,
      `<PERSON><PERSON><PERSON><PERSON><PERSON>N` text,
      `FILE_PATH` varchar(255) NOT NULL,
      `CREATED_BY` varchar(10) NOT NULL,
      `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `STATUS` tinyint(1) NOT NULL DEFAULT '1',
      PRIMAR<PERSON> KEY (`ID`),
      <PERSON><PERSON><PERSON> `idx_kategori` (`ID_KATEGORI`),
      <PERSON><PERSON><PERSON> `idx_jenis_dok` (`ID_JENIS_DOK`),
      KEY `idx_unit` (`UNIT`),
      KEY `idx_status` (`STATUS`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8",

    // Tabel edoc_dokumentasi_detail
    "CREATE TABLE IF NOT EXISTS `edoc_dokumentasi_detail` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `ID_DOKUMENTASI` int(11) NOT NULL,
      `FILE_PATH` varchar(255) NOT NULL,
      `TIPE_FILE` enum('foto','video') NOT NULL,
      `LOKASI_FILE` varchar(255) NOT NULL,
      `UPLOADED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `STATUS` tinyint(1) NOT NULL DEFAULT '1',
      PRIMARY KEY (`ID`),
      KEY `idx_dokumentasi` (`ID_DOKUMENTASI`),
      KEY `idx_tipe_file` (`TIPE_FILE`),
      KEY `idx_status` (`STATUS`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8",

    // Tabel edoc_refrensi (jika belum ada)
    "CREATE TABLE IF NOT EXISTS `edoc_refrensi` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `JENIS` tinyint(2) NOT NULL COMMENT '1: Kategori Dokumentasi, 2: Jenis Dokumentasi',
      `DESKRIPSI` varchar(255) NOT NULL,
      `STATUS` tinyint(1) NOT NULL DEFAULT '1',
      `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`ID`),
      KEY `idx_jenis` (`JENIS`),
      KEY `idx_status` (`STATUS`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8"
];

// Data sample untuk edoc_refrensi
$insertQueries = [
    "INSERT IGNORE INTO `edoc_refrensi` (`JENIS`, `DESKRIPSI`, `STATUS`) VALUES
    (1, 'Kegiatan Pelatihan', 1),
    (1, 'Kegiatan Seminar', 1),
    (1, 'Kegiatan Workshop', 1),
    (1, 'Kegiatan Rapat', 1),
    (1, 'Kegiatan Sosialisasi', 1),
    (2, 'Dokumentasi Foto', 1),
    (2, 'Dokumentasi Video', 1),
    (2, 'Dokumentasi Campuran', 1)"
];

echo "<h3>Membuat Tabel...</h3>";

// Eksekusi query pembuatan tabel
foreach ($queries as $index => $query) {
    $tableName = ($index == 0) ? 'edoc_dokumentasi' : (($index == 1) ? 'edoc_dokumentasi_detail' : 'edoc_refrensi');
    
    if (mysqli_query($koneksi, $query)) {
        echo "<p style='color: green;'>✓ Tabel $tableName berhasil dibuat/sudah ada</p>";
    } else {
        echo "<p style='color: red;'>✗ Error membuat tabel $tableName: " . mysqli_error($koneksi) . "</p>";
    }
}

echo "<h3>Menambahkan Data Sample...</h3>";

// Eksekusi query insert data
foreach ($insertQueries as $query) {
    if (mysqli_query($koneksi, $query)) {
        echo "<p style='color: green;'>✓ Data sample berhasil ditambahkan</p>";
    } else {
        echo "<p style='color: red;'>✗ Error menambahkan data sample: " . mysqli_error($koneksi) . "</p>";
    }
}

// Cek apakah tabel sudah ada
echo "<h3>Verifikasi Tabel...</h3>";

$tables = ['edoc_dokumentasi', 'edoc_dokumentasi_detail', 'edoc_refrensi'];
foreach ($tables as $table) {
    $result = mysqli_query($koneksi, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Tabel $table: OK</p>";
        
        // Hitung jumlah record
        $countResult = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM $table");
        $count = mysqli_fetch_array($countResult)['total'];
        echo "<p style='margin-left: 20px; color: blue;'>→ Jumlah record: $count</p>";
    } else {
        echo "<p style='color: red;'>✗ Tabel $table: TIDAK DITEMUKAN</p>";
    }
}

echo "<h3>Instalasi Selesai!</h3>";
echo "<p><a href='index.php'>← Kembali ke Dokumentasi Kegiatan</a></p>";
?>

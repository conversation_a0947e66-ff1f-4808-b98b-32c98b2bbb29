@-webkit-keyframes line-spin-fade-loader
{
    50%
    {
        opacity: .3;
    }
    100%
    {
        opacity: 1;
    }
}

@-moz-keyframes line-spin-fade-loader
{
    50%
    {
        opacity: .3;
    }
    100%
    {
        opacity: 1;
    }
}

@-o-keyframes line-spin-fade-loader
{
    50%
    {
        opacity: .3;
    }
    100%
    {
        opacity: 1;
    }
}

@keyframes line-spin-fade-loader
{
    50%
    {
        opacity: .3;
    }
    100%
    {
        opacity: 1;
    }
}

.line-spin-fade-loader
{
    position: relative;
    top: -10px;
    right: -4px;
}
.line-spin-fade-loader > div:nth-child(1)
{
    top: 20px;
    right: 0;

    -webkit-animation: line-spin-fade-loader 1.2s -.84s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.84s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.84s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.84s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(2)
{
    top: 13.63636px;
    right: 13.63636px;

    -webkit-transform: rotate(45deg);
       -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
         -o-transform: rotate(45deg);
            transform: rotate(45deg);
    -webkit-animation: line-spin-fade-loader 1.2s -.72s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.72s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.72s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.72s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(3)
{
    top: 0;
    right: 20px;

    -webkit-transform: rotate(-90deg);
       -moz-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
         -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
    -webkit-animation: line-spin-fade-loader 1.2s -.6s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.6s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.6s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.6s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(4)
{
    top: -13.63636px;
    right: 13.63636px;

    -webkit-transform: rotate(-45deg);
       -moz-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
         -o-transform: rotate(-45deg);
            transform: rotate(-45deg);
    -webkit-animation: line-spin-fade-loader 1.2s -.48s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.48s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.48s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.48s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(5)
{
    top: -20px;
    right: 0;

    -webkit-animation: line-spin-fade-loader 1.2s -.36s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.36s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.36s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.36s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(6)
{
    top: -13.63636px;
    right: -13.63636px;

    -webkit-transform: rotate(45deg);
       -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
         -o-transform: rotate(45deg);
            transform: rotate(45deg);
    -webkit-animation: line-spin-fade-loader 1.2s -.24s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.24s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.24s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.24s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(7)
{
    top: 0;
    right: -20px;

    -webkit-transform: rotate(-90deg);
       -moz-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
         -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
    -webkit-animation: line-spin-fade-loader 1.2s -.12s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s -.12s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s -.12s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s -.12s infinite ease-in-out;
}
.line-spin-fade-loader > div:nth-child(8)
{
    top: 13.63636px;
    right: -13.63636px;

    -webkit-transform: rotate(-45deg);
       -moz-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
         -o-transform: rotate(-45deg);
            transform: rotate(-45deg);
    -webkit-animation: line-spin-fade-loader 1.2s 0s infinite ease-in-out;
       -moz-animation: line-spin-fade-loader 1.2s 0s infinite ease-in-out;
         -o-animation: line-spin-fade-loader 1.2s 0s infinite ease-in-out;
            animation: line-spin-fade-loader 1.2s 0s infinite ease-in-out;
}
.line-spin-fade-loader > div
{
    position: absolute;

    width: 4px;
    width: 5px;
    height: 3.45rem;
    height: 15px; 
    margin: 2px;

    border-radius: 2px;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

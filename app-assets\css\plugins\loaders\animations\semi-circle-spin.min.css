@-webkit-keyframes spin-rotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin-rotate{0%{-moz-transform:rotate(0);transform:rotate(0)}50%{-moz-transform:rotate(180deg);transform:rotate(180deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin-rotate{0%{-o-transform:rotate(0);transform:rotate(0)}50%{-o-transform:rotate(180deg);transform:rotate(180deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin-rotate{0%{-webkit-transform:rotate(0);-moz-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-o-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.semi-circle-spin{position:relative;overflow:hidden;width:35px;height:35px}.semi-circle-spin>div{position:absolute;width:100%;height:100%;-webkit-animation:spin-rotate .6s 0s infinite linear;-moz-animation:spin-rotate .6s 0s infinite linear;-o-animation:spin-rotate .6s 0s infinite linear;animation:spin-rotate .6s 0s infinite linear;border-width:0;border-radius:100%;background-image:-webkit-gradient(linear,left top,left bottom,from(transparent),color-stop(70%,transparent),color-stop(30%,#404e67),to(#404e67));background-image:-webkit-linear-gradient(transparent 0,transparent 70%,#404e67 30%,#404e67 100%);background-image:-moz-linear-gradient(transparent 0,transparent 70%,#404e67 30%,#404e67 100%);background-image:-o-linear-gradient(transparent 0,transparent 70%,#404e67 30%,#404e67 100%);background-image:linear-gradient(transparent 0,transparent 70%,#404e67 30%,#404e67 100%)}
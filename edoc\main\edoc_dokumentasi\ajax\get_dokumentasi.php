<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

try {
    if (empty($_POST['id'])) {
        throw new Exception('ID dokumentasi tidak ditemukan');
    }

    $id = intval($_POST['id']);

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND ed.UNIT = '$user'";
    }

    // Query data utama dokumentasi
    $sqlMain = "SELECT 
                    ed.*, 
                    er1.DESKRIPSI as KATEGORI,
                    er2.DESKRIPSI as JENIS_DOK,
                    u.UNIT as NAMA_UNIT
                FROM edoc_dokumentasi ed
                LEFT JOIN edoc_refrensi er1 ON ed.ID_KATEGORI = er1.ID
                LEFT JOIN edoc_refrensi er2 ON ed.ID_JENIS_DOK = er2.ID
                LEFT JOIN unit u ON ed.UNIT = u.ID
                WHERE ed.ID = $id AND ed.STATUS = 1 $whereAccess";

    $resultMain = mysqli_query($koneksi, $sqlMain);
    
    if (!$resultMain || mysqli_num_rows($resultMain) == 0) {
        throw new Exception('Data dokumentasi tidak ditemukan');
    }

    $dataMain = mysqli_fetch_array($resultMain);

    // Query data detail file
    $sqlDetail = "SELECT * FROM edoc_dokumentasi_detail 
                  WHERE ID_DOKUMENTASI = $id AND STATUS = 1 
                  ORDER BY TIPE_FILE, UPLOADED_AT";

    $resultDetail = mysqli_query($koneksi, $sqlDetail);
    
    $files = [];
    while ($file = mysqli_fetch_array($resultDetail)) {
        $files[] = [
            'id' => $file['ID'],
            'file_path' => $file['FILE_PATH'],
            'tipe_file' => $file['TIPE_FILE'],
            'lokasi_file' => $file['LOKASI_FILE'],
            'uploaded_at' => $file['UPLOADED_AT']
        ];
    }

    // Query untuk dropdown kategori
    $qkategori = mysqli_query($koneksi, "SELECT * FROM edoc_refrensi WHERE JENIS = 1 AND STATUS = 1 ORDER BY ID");
    $kategoriOptions = [];
    while($kategori = mysqli_fetch_array($qkategori)) {
        $kategoriOptions[] = [
            'id' => $kategori['ID'],
            'deskripsi' => $kategori['DESKRIPSI']
        ];
    }

    // Query untuk dropdown jenis dokumentasi
    $qjenis = mysqli_query($koneksi, "SELECT * FROM edoc_refrensi WHERE JENIS = 2 AND STATUS = 1 ORDER BY ID");
    $jenisOptions = [];
    while($jenis = mysqli_fetch_array($qjenis)) {
        $jenisOptions[] = [
            'id' => $jenis['ID'],
            'deskripsi' => $jenis['DESKRIPSI']
        ];
    }

    // Query untuk dropdown unit (hanya untuk admin)
    $unitOptions = [];
    if ($hak_akses == '1') {
        $qunit = mysqli_query($koneksi, "SELECT * FROM unit ORDER BY UNIT");
        while($unit = mysqli_fetch_array($qunit)) {
            $unitOptions[] = [
                'id' => $unit['ID'],
                'unit' => $unit['UNIT']
            ];
        }
    }

    $response = [
        'status' => 'success',
        'data' => [
            'main' => [
                'id' => $dataMain['ID'],
                'judul' => $dataMain['JUDUL'],
                'id_kategori' => $dataMain['ID_KATEGORI'],
                'kategori' => $dataMain['KATEGORI'],
                'tanggal' => $dataMain['TANGGAL'],
                'lokasi' => $dataMain['LOKASI'],
                'unit' => $dataMain['UNIT'],
                'nama_unit' => $dataMain['NAMA_UNIT'],
                'id_jenis_dok' => $dataMain['ID_JENIS_DOK'],
                'jenis_dok' => $dataMain['JENIS_DOK'],
                'keterangan' => $dataMain['KETERANGAN'],
                'FILE_PATH' => $dataMain['FILE_PATH'],
                'created_at' => $dataMain['CREATED_AT']
            ],
            'files' => $files,
            'options' => [
                'kategori' => $kategoriOptions,
                'jenis' => $jenisOptions,
                'unit' => $unitOptions
            ]
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>

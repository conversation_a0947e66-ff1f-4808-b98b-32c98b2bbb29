.slider-white .noUi-connect
{
    background: #fff !important;
}

.slider-white.noUi-connect
{
    background: #fff !important;
}

.slider-white .noUi-handle
{
    border-color: #fff !important;
}

.slider-white.circle-filled .noUi-handle,
.slider-white.square .noUi-handle
{
    background: #fff !important;
}

.slider-black .noUi-connect
{
    background: #000 !important;
}

.slider-black.noUi-connect
{
    background: #000 !important;
}

.slider-black .noUi-handle
{
    border-color: #000 !important;
}

.slider-black.circle-filled .noUi-handle,
.slider-black.square .noUi-handle
{
    background: #000 !important;
}

.slider-primary .noUi-connect
{
    background: #00b5b8 !important;
}

.slider-primary.noUi-connect
{
    background: #00b5b8 !important;
}

.slider-primary .noUi-handle
{
    border-color: #00b5b8 !important;
}

.slider-primary.circle-filled .noUi-handle,
.slider-primary.square .noUi-handle
{
    background: #00b5b8 !important;
}

.slider-success .noUi-connect
{
    background: #16d39a !important;
}

.slider-success.noUi-connect
{
    background: #16d39a !important;
}

.slider-success .noUi-handle
{
    border-color: #16d39a !important;
}

.slider-success.circle-filled .noUi-handle,
.slider-success.square .noUi-handle
{
    background: #16d39a !important;
}

.slider-info .noUi-connect
{
    background: #2dcee3 !important;
}

.slider-info.noUi-connect
{
    background: #2dcee3 !important;
}

.slider-info .noUi-handle
{
    border-color: #2dcee3 !important;
}

.slider-info.circle-filled .noUi-handle,
.slider-info.square .noUi-handle
{
    background: #2dcee3 !important;
}

.slider-warning .noUi-connect
{
    background: #ffa87d !important;
}

.slider-warning.noUi-connect
{
    background: #ffa87d !important;
}

.slider-warning .noUi-handle
{
    border-color: #ffa87d !important;
}

.slider-warning.circle-filled .noUi-handle,
.slider-warning.square .noUi-handle
{
    background: #ffa87d !important;
}

.slider-danger .noUi-connect
{
    background: #ff7588 !important;
}

.slider-danger.noUi-connect
{
    background: #ff7588 !important;
}

.slider-danger .noUi-handle
{
    border-color: #ff7588 !important;
}

.slider-danger.circle-filled .noUi-handle,
.slider-danger.square .noUi-handle
{
    background: #ff7588 !important;
}

.slider-red .noUi-connect
{
    background: #f44336 !important;
}

.slider-red.noUi-connect
{
    background: #f44336 !important;
}

.slider-red .noUi-handle
{
    border-color: #f44336 !important;
}

.slider-red.circle-filled .noUi-handle,
.slider-red.square .noUi-handle
{
    background: #f44336 !important;
}

.slider-pink .noUi-connect
{
    background: #e91e63 !important;
}

.slider-pink.noUi-connect
{
    background: #e91e63 !important;
}

.slider-pink .noUi-handle
{
    border-color: #e91e63 !important;
}

.slider-pink.circle-filled .noUi-handle,
.slider-pink.square .noUi-handle
{
    background: #e91e63 !important;
}

.slider-purple .noUi-connect
{
    background: #9c27b0 !important;
}

.slider-purple.noUi-connect
{
    background: #9c27b0 !important;
}

.slider-purple .noUi-handle
{
    border-color: #9c27b0 !important;
}

.slider-purple.circle-filled .noUi-handle,
.slider-purple.square .noUi-handle
{
    background: #9c27b0 !important;
}

.slider-blue .noUi-connect
{
    background: #2196f3 !important;
}

.slider-blue.noUi-connect
{
    background: #2196f3 !important;
}

.slider-blue .noUi-handle
{
    border-color: #2196f3 !important;
}

.slider-blue.circle-filled .noUi-handle,
.slider-blue.square .noUi-handle
{
    background: #2196f3 !important;
}

.slider-cyan .noUi-connect
{
    background: #00bcd4 !important;
}

.slider-cyan.noUi-connect
{
    background: #00bcd4 !important;
}

.slider-cyan .noUi-handle
{
    border-color: #00bcd4 !important;
}

.slider-cyan.circle-filled .noUi-handle,
.slider-cyan.square .noUi-handle
{
    background: #00bcd4 !important;
}

.slider-teal .noUi-connect
{
    background: #009688 !important;
}

.slider-teal.noUi-connect
{
    background: #009688 !important;
}

.slider-teal .noUi-handle
{
    border-color: #009688 !important;
}

.slider-teal.circle-filled .noUi-handle,
.slider-teal.square .noUi-handle
{
    background: #009688 !important;
}

.slider-yellow .noUi-connect
{
    background: #ffeb3b !important;
}

.slider-yellow.noUi-connect
{
    background: #ffeb3b !important;
}

.slider-yellow .noUi-handle
{
    border-color: #ffeb3b !important;
}

.slider-yellow.circle-filled .noUi-handle,
.slider-yellow.square .noUi-handle
{
    background: #ffeb3b !important;
}

.slider-amber .noUi-connect
{
    background: #ffc107 !important;
}

.slider-amber.noUi-connect
{
    background: #ffc107 !important;
}

.slider-amber .noUi-handle
{
    border-color: #ffc107 !important;
}

.slider-amber.circle-filled .noUi-handle,
.slider-amber.square .noUi-handle
{
    background: #ffc107 !important;
}

.slider-blue-grey .noUi-connect
{
    background: #607d8b !important;
}

.slider-blue-grey.noUi-connect
{
    background: #607d8b !important;
}

.slider-blue-grey .noUi-handle
{
    border-color: #607d8b !important;
}

.slider-blue-grey.circle-filled .noUi-handle,
.slider-blue-grey.square .noUi-handle
{
    background: #607d8b !important;
}

.slider-grey-blue .noUi-connect
{
    background: #1b2942 !important;
}

.slider-grey-blue.noUi-connect
{
    background: #1b2942 !important;
}

.slider-grey-blue .noUi-handle
{
    border-color: #1b2942 !important;
}

.slider-grey-blue.circle-filled .noUi-handle,
.slider-grey-blue.square .noUi-handle
{
    background: #1b2942 !important;
}

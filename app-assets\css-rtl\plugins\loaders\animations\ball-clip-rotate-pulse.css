@-webkit-keyframes rotate
{
    0%
    {
        -webkit-transform: rotate(0deg) scale(1);
                transform: rotate(0deg) scale(1);
    }
    50%
    {
        -webkit-transform: rotate(-180deg) scale(.6);
                transform: rotate(-180deg) scale(.6);
    }
    100%
    {
        -webkit-transform: rotate(-360deg) scale(1);
                transform: rotate(-360deg) scale(1);
    }
}

@-moz-keyframes rotate
{
    0%
    {
        -moz-transform: rotate(0deg) scale(1);
             transform: rotate(0deg) scale(1);
    }
    50%
    {
        -moz-transform: rotate(-180deg) scale(.6);
             transform: rotate(-180deg) scale(.6);
    }
    100%
    {
        -moz-transform: rotate(-360deg) scale(1);
             transform: rotate(-360deg) scale(1);
    }
}

@-o-keyframes rotate
{
    0%
    {
        -o-transform: rotate(0deg) scale(1);
           transform: rotate(0deg) scale(1);
    }
    50%
    {
        -o-transform: rotate(-180deg) scale(.6);
           transform: rotate(-180deg) scale(.6);
    }
    100%
    {
        -o-transform: rotate(-360deg) scale(1);
           transform: rotate(-360deg) scale(1);
    }
}

@keyframes rotate
{
    0%
    {
        -webkit-transform: rotate(0deg) scale(1);
           -moz-transform: rotate(0deg) scale(1);
             -o-transform: rotate(0deg) scale(1);
                transform: rotate(0deg) scale(1);
    }
    50%
    {
        -webkit-transform: rotate(-180deg) scale(.6);
           -moz-transform: rotate(-180deg) scale(.6);
             -o-transform: rotate(-180deg) scale(.6);
                transform: rotate(-180deg) scale(.6);
    }
    100%
    {
        -webkit-transform: rotate(-360deg) scale(1);
           -moz-transform: rotate(-360deg) scale(1);
             -o-transform: rotate(-360deg) scale(1);
                transform: rotate(-360deg) scale(1);
    }
}

@-webkit-keyframes scale
{
    30%
    {
        -webkit-transform: scale(.3);
                transform: scale(.3);
    }
    100%
    {
        -webkit-transform: scale(1);
                transform: scale(1);
    }
}

@-moz-keyframes scale
{
    30%
    {
        -moz-transform: scale(.3);
             transform: scale(.3);
    }
    100%
    {
        -moz-transform: scale(1);
             transform: scale(1);
    }
}

@-o-keyframes scale
{
    30%
    {
        -o-transform: scale(.3);
           transform: scale(.3);
    }
    100%
    {
        -o-transform: scale(1);
           transform: scale(1);
    }
}

@keyframes scale
{
    30%
    {
        -webkit-transform: scale(.3);
           -moz-transform: scale(.3);
             -o-transform: scale(.3);
                transform: scale(.3);
    }
    100%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);
    }
}

.ball-clip-rotate-pulse
{
    position: relative;

    -webkit-transform: translateY(-15px);
       -moz-transform: translateY(-15px);
        -ms-transform: translateY(-15px);
         -o-transform: translateY(-15px);
            transform: translateY(-15px);
}
.ball-clip-rotate-pulse > div
{
    position: absolute;
    top: 0;
    right: 0;

    border-radius: 100%; 

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}
.ball-clip-rotate-pulse > div:first-child
{
    top: 7px;
    right: -7px;

    width: 16px;
    height: 16px;

    -webkit-animation: scale 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
       -moz-animation: scale 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
         -o-animation: scale 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
            animation: scale 1s 0s cubic-bezier(.09, .57, .49, .9) infinite; 

    background: #404e67;
}
.ball-clip-rotate-pulse > div:last-child
{
    position: absolute;
    top: -2px;
    right: -16px;

    width: 30px;
    height: 30px;

    -webkit-animation: rotate 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
       -moz-animation: rotate 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
         -o-animation: rotate 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
            animation: rotate 1s 0s cubic-bezier(.09, .57, .49, .9) infinite;
    -webkit-animation-duration: 1s;
       -moz-animation-duration: 1s;
         -o-animation-duration: 1s;
            animation-duration: 1s; 

    border: 2px solid #404e67;
    border: 2px solid;
    border-color: #404e67 transparent #404e67 transparent;
    background: transparent;
}

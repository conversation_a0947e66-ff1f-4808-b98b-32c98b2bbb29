@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-moz-keyframes rotate {
  0% {
    -moz-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -moz-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -moz-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-o-keyframes rotate {
  0% {
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    -moz-transform: rotate(180deg) scale(0.6);
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-webkit-keyframes scale {
  30% {
    -webkit-transform: scale(0.3);
    transform: scale(0.3); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

@-moz-keyframes scale {
  30% {
    -moz-transform: scale(0.3);
    transform: scale(0.3); }
  100% {
    -moz-transform: scale(1);
    transform: scale(1); } }

@-o-keyframes scale {
  30% {
    -o-transform: scale(0.3);
    transform: scale(0.3); }
  100% {
    -o-transform: scale(1);
    transform: scale(1); } }

@keyframes scale {
  30% {
    -webkit-transform: scale(0.3);
    -moz-transform: scale(0.3);
    -o-transform: scale(0.3);
    transform: scale(0.3); }
  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }

.ball-clip-rotate-pulse {
  position: relative;
  -webkit-transform: translateY(-15px);
  -moz-transform: translateY(-15px);
  -ms-transform: translateY(-15px);
  -o-transform: translateY(-15px);
  transform: translateY(-15px); }
  .ball-clip-rotate-pulse > div {
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    top: 0px;
    left: 0px;
    border-radius: 100%; }
    .ball-clip-rotate-pulse > div:first-child {
      background: #404E67;
      height: 16px;
      width: 16px;
      top: 7px;
      left: -7px;
      -webkit-animation: scale 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      -moz-animation: scale 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      -o-animation: scale 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      animation: scale 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite; }
    .ball-clip-rotate-pulse > div:last-child {
      position: absolute;
      border: 2px solid #404E67;
      width: 30px;
      height: 30px;
      left: -16px;
      top: -2px;
      background: transparent;
      border: 2px solid;
      border-color: #404E67 transparent #404E67 transparent;
      -webkit-animation: rotate 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      -moz-animation: rotate 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      -o-animation: rotate 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      animation: rotate 1s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
      -webkit-animation-duration: 1s;
      -moz-animation-duration: 1s;
      -o-animation-duration: 1s;
      animation-duration: 1s; }

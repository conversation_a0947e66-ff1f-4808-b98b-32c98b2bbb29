@-webkit-keyframes line-scale-party
{
    0%
    {
        -webkit-transform: scale(1);
                transform: scale(1);
    }
    50%
    {
        -webkit-transform: scale(.5);
                transform: scale(.5);
    }
    100%
    {
        -webkit-transform: scale(1);
                transform: scale(1);
    }
}

@-moz-keyframes line-scale-party
{
    0%
    {
        -moz-transform: scale(1);
             transform: scale(1);
    }
    50%
    {
        -moz-transform: scale(.5);
             transform: scale(.5);
    }
    100%
    {
        -moz-transform: scale(1);
             transform: scale(1);
    }
}

@-o-keyframes line-scale-party
{
    0%
    {
        -o-transform: scale(1);
           transform: scale(1);
    }
    50%
    {
        -o-transform: scale(.5);
           transform: scale(.5);
    }
    100%
    {
        -o-transform: scale(1);
           transform: scale(1);
    }
}

@keyframes line-scale-party
{
    0%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);
    }
    50%
    {
        -webkit-transform: scale(.5);
           -moz-transform: scale(.5);
             -o-transform: scale(.5);
                transform: scale(.5);
    }
    100%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);
    }
}

.line-scale-party > div:nth-child(1)
{
    -webkit-animation-duration: .85s;
       -moz-animation-duration: .85s;
         -o-animation-duration: .85s;
            animation-duration: .85s; 
    -webkit-animation-delay: .31s;
       -moz-animation-delay: .31s;
         -o-animation-delay: .31s;
            animation-delay: .31s;
}

.line-scale-party > div:nth-child(2)
{
    -webkit-animation-duration: .64s;
       -moz-animation-duration: .64s;
         -o-animation-duration: .64s;
            animation-duration: .64s; 
    -webkit-animation-delay: .71s;
       -moz-animation-delay: .71s;
         -o-animation-delay: .71s;
            animation-delay: .71s;
}

.line-scale-party > div:nth-child(3)
{
    -webkit-animation-duration: .75s;
       -moz-animation-duration: .75s;
         -o-animation-duration: .75s;
            animation-duration: .75s; 
    -webkit-animation-delay: .75s;
       -moz-animation-delay: .75s;
         -o-animation-delay: .75s;
            animation-delay: .75s;
}

.line-scale-party > div:nth-child(4)
{
    -webkit-animation-duration: 1.23s;
       -moz-animation-duration: 1.23s;
         -o-animation-duration: 1.23s;
            animation-duration: 1.23s; 
    -webkit-animation-delay: .37s;
       -moz-animation-delay: .37s;
         -o-animation-delay: .37s;
            animation-delay: .37s;
}

.line-scale-party > div
{
    display: inline-block;

    width: 4px;
    height: 3.45rem;
    margin: 2px;

    -webkit-animation-name: line-scale-party;
       -moz-animation-name: line-scale-party;
         -o-animation-name: line-scale-party;
            animation-name: line-scale-party;
    -webkit-animation-delay: 0;
       -moz-animation-delay: 0;
         -o-animation-delay: 0;
            animation-delay: 0; 
    -webkit-animation-iteration-count: infinite;
       -moz-animation-iteration-count: infinite;
         -o-animation-iteration-count: infinite;
            animation-iteration-count: infinite;

    border-radius: 2px;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

@-webkit-keyframes ball-scale-ripple {
  0% {
    -webkit-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@-moz-keyframes ball-scale-ripple {
  0% {
    -moz-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -moz-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@-o-keyframes ball-scale-ripple {
  0% {
    -o-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@keyframes ball-scale-ripple {
  0% {
    -webkit-transform: scale(0.1);
    -moz-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

.ball-scale-ripple > div {
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  height: 50px;
  width: 50px;
  border-radius: 100%;
  border: 2px solid #404E67;
  -webkit-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
  -moz-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
  -o-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
  animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8); }

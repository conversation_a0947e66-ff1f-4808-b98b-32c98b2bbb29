<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    die('Access denied');
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

try {
    if (empty($_GET['id'])) {
        throw new Exception('ID file tidak ditemukan');
    }

    $fileId = intval($_GET['id']);
    $withWatermark = isset($_GET['watermark']) ? intval($_GET['watermark']) : 1;

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND ed.UNIT = '$user'";
    }

    // Query untuk mendapatkan info file
    $sql = "SELECT 
                edd.*, 
                ed.JUDUL, ed.TANGGAL, ed.UNIT
            FROM edoc_dokumentasi_detail edd
            INNER JOIN edoc_dokumentasi ed ON edd.ID_DOKUMENTASI = ed.ID
            WHERE edd.ID = $fileId 
            AND edd.STATUS = 1 
            AND ed.STATUS = 1 
            AND edd.TIPE_FILE = 'foto' 
            $whereAccess";

    $result = mysqli_query($koneksi, $sql);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        throw new Exception('File tidak ditemukan atau Anda tidak memiliki akses');
    }

    $fileData = mysqli_fetch_array($result);
    
    // Path file asli
    $originalPath = '../../../edoc/main' . $fileData['LOKASI_FILE'] . '/' . $fileData['FILE_PATH'];
    
    if (!file_exists($originalPath)) {
        throw new Exception('File fisik tidak ditemukan');
    }

    // Jika tidak perlu watermark (hanya admin yang bisa akses original)
    if (!$withWatermark && $hak_akses == '1') {
        // Download file original
        header('Content-Type: ' . mime_content_type($originalPath));
        header('Content-Disposition: attachment; filename="' . $fileData['FILE_PATH'] . '"');
        header('Content-Length: ' . filesize($originalPath));
        readfile($originalPath);
        exit;
    }

    // Proses watermark
    $imageInfo = getimagesize($originalPath);
    if (!$imageInfo) {
        throw new Exception('File bukan gambar yang valid');
    }

    $imageType = $imageInfo[2];
    
    // Buat resource gambar berdasarkan tipe
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($originalPath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($originalPath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($originalPath);
            break;
        default:
            throw new Exception('Tipe gambar tidak didukung');
    }

    if (!$image) {
        throw new Exception('Gagal memproses gambar');
    }

    // Path logo watermark
    $logoPath = '../../../edoc/assets/images/logo.png';
    
    if (file_exists($logoPath)) {
        $logo = imagecreatefrompng($logoPath);
        
        if ($logo) {
            // Dapatkan dimensi gambar dan logo
            $imageWidth = imagesx($image);
            $imageHeight = imagesy($image);
            $logoWidth = imagesx($logo);
            $logoHeight = imagesy($logo);
            
            // Hitung ukuran logo (20% dari lebar gambar)
            $newLogoWidth = $imageWidth * 0.2;
            $newLogoHeight = ($logoHeight / $logoWidth) * $newLogoWidth;
            
            // Posisi tengah
            $logoX = ($imageWidth - $newLogoWidth) / 2;
            $logoY = ($imageHeight - $newLogoHeight) / 2;
            
            // Buat logo dengan transparansi 20%
            $logoResized = imagecreatetruecolor($newLogoWidth, $newLogoHeight);
            imagealphablending($logoResized, false);
            imagesavealpha($logoResized, true);
            
            // Copy dan resize logo
            imagecopyresampled($logoResized, $logo, 0, 0, 0, 0, 
                             $newLogoWidth, $newLogoHeight, $logoWidth, $logoHeight);
            
            // Set transparansi 20% (80% opacity)
            $transparent = imagecolorallocatealpha($logoResized, 255, 255, 255, 102);
            imagefill($logoResized, 0, 0, $transparent);
            
            // Terapkan watermark ke gambar utama
            imagecopymerge($image, $logoResized, $logoX, $logoY, 0, 0, 
                          $newLogoWidth, $newLogoHeight, 20);
            
            imagedestroy($logo);
            imagedestroy($logoResized);
        }
    }

    // Set header untuk download
    $fileName = 'watermarked_' . $fileData['FILE_PATH'];
    
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            header('Content-Type: image/jpeg');
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            imagejpeg($image, null, 90);
            break;
        case IMAGETYPE_PNG:
            header('Content-Type: image/png');
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            imagepng($image);
            break;
        case IMAGETYPE_GIF:
            header('Content-Type: image/gif');
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            imagegif($image);
            break;
    }

    imagedestroy($image);

} catch (Exception $e) {
    header('HTTP/1.1 404 Not Found');
    echo 'Error: ' . $e->getMessage();
}
?>

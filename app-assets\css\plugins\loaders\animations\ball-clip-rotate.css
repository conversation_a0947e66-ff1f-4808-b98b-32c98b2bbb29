@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-moz-keyframes rotate {
  0% {
    -moz-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -moz-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -moz-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-o-keyframes rotate {
  0% {
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    -moz-transform: rotate(180deg) scale(0.6);
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

.ball-clip-rotate > div {
  background-color: #404E67;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  border: 2px solid #404E67;
  border-bottom-color: transparent;
  height: 25px;
  width: 25px;
  background: transparent !important;
  display: inline-block;
  -webkit-animation: rotate 0.75s 0s linear infinite;
  -moz-animation: rotate 0.75s 0s linear infinite;
  -o-animation: rotate 0.75s 0s linear infinite;
  animation: rotate 0.75s 0s linear infinite; }

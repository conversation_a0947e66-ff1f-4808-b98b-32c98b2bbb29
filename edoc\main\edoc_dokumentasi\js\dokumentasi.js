$(document).ready(function() {
    // Konfigurasi toastr
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };

    // Inisialisasi DataTable
    var table = $('#tableDokumentasi').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "ajax/dokumentasi_serverside.php",
            "type": "POST"
        },
        "columns": [
            { "data": 0, "className": "text-center", "width": "5%" },
            { "data": 1, "className": "text-center", "width": "10%" },
            { "data": 2, "className": "text-left", "width": "25%" },
            { "data": 3, "className": "text-left", "width": "15%" },
            { "data": 4, "className": "text-left", "width": "15%" },
            { "data": 5, "className": "text-left", "width": "10%" },
            { "data": 6, "className": "text-left", "width": "10%" },
            { "data": 7, "className": "text-center", "width": "10%", "orderable": false }
        ],
        "order": [[ 1, "desc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Preview foto saat dipilih
    $('#inputFoto').on('change', function() {
        var files = this.files;
        var previewContainer = $('#previewFoto');
        previewContainer.empty();
        
        if (files.length > 0) {
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                if (file.type.startsWith('image/')) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var img = $('<img>').attr('src', e.target.result).addClass('preview-image img-thumbnail');
                        previewContainer.append(img);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }
    });

    // Tambah input video
    $('#btnTambahVideo').on('click', function() {
        var videoInput = `
            <div class="video-input-group">
                <div class="input-group">
                    <input type="url" class="form-control" name="video[]" placeholder="https://youtube.com/watch?v=...">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-danger btn-remove-video">
                            <i class="fa fa-trash"></i>
                        </button>
                    </span>
                </div>
            </div>
        `;
        $('#videoInputs').append(videoInput);
    });

    // Hapus input video
    $(document).on('click', '.btn-remove-video', function() {
        $(this).closest('.video-input-group').remove();
    });

    // Submit form tambah dokumentasi
    $('#formTambahDokumentasi').on('submit', function(e) {
        e.preventDefault();
        
        // Check HTML5 form validation
        if (!this.checkValidity()) {
            // Let the browser handle the validation display
            return false;
        }
        
        var formData = new FormData(this);
        
        // Don't check for files initially, let the server handle the validation
        // to ensure consistent validation messages
        
        // Tampilkan loading
        Swal.fire({
            title: 'Menyimpan...',
            text: 'Sedang memproses data dokumentasi',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });
        
        $.ajax({
            url: 'process/simpan_dokumentasi.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                Swal.close();
                try {
                    var result = JSON.parse(response);
                    if (result.status === 'success') {
                        toastr.success(result.message);
                        $('#modalTambahDokumentasi').modal('hide');
                        $('#formTambahDokumentasi')[0].reset();
                        $('#previewFoto').empty();
                        // Reset video inputs
                        $('#videoInputs').html(`
                            <div class="video-input-group">
                                <input type="url" class="form-control" name="video[]" placeholder="https://youtube.com/watch?v=...">
                            </div>
                        `);
                        table.ajax.reload();
                    } else {
                        toastr.error(result.message);
                    }
                } catch (e) {
                    toastr.error('Terjadi kesalahan dalam memproses response');
                    console.log(response);
                }
            },
            error: function(xhr, status, error) {
                Swal.close();
                toastr.error('Terjadi kesalahan: ' + error);
            }
        });
    });

    // Reset form saat modal ditutup
    $('#modalTambahDokumentasi').on('hidden.bs.modal', function() {
        $('#formTambahDokumentasi')[0].reset();
        $('#previewFoto').empty();
        $('#videoInputs').html(`
            <div class="video-input-group">
                <input type="url" class="form-control" name="video[]" placeholder="https://youtube.com/watch?v=...">
            </div>
        `);
    });

    // Preview foto edit saat dipilih
    $('#editInputFoto').on('change', function() {
        var files = this.files;
        var previewContainer = $('#editPreviewFoto');
        previewContainer.empty();

        if (files.length > 0) {
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                if (file.type.startsWith('image/')) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var img = $('<img>').attr('src', e.target.result).addClass('preview-image img-thumbnail');
                        previewContainer.append(img);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }
    });

    // Tambah input video edit
    $('#btnTambahVideoEdit').on('click', function() {
        var videoInput = `
            <div class="video-input-group">
                <div class="input-group">
                    <input type="url" class="form-control" name="video_baru[]" placeholder="https://youtube.com/watch?v=...">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-danger btn-remove-video">
                            <i class="fa fa-trash"></i>
                        </button>
                    </span>
                </div>
            </div>
        `;
        $('#editVideoInputs').append(videoInput);
    });

    // Submit form edit dokumentasi
    $('#formEditDokumentasi').on('submit', function(e) {
        e.preventDefault();
        
        // Check HTML5 form validation
        if (!this.checkValidity()) {
            // Let the browser handle the validation display
            return false;
        }

        var formData = new FormData(this);

        // Tampilkan loading
        Swal.fire({
            title: 'Mengupdate...',
            text: 'Sedang memproses perubahan dokumentasi',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: 'process/update_dokumentasi.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                Swal.close();
                try {
                    var result = JSON.parse(response);
                    if (result.status === 'success') {
                        toastr.success(result.message);
                        $('#modalEditDokumentasi').modal('hide');
                        table.ajax.reload();
                    } else {
                        toastr.error(result.message);
                    }
                } catch (e) {
                    toastr.error('Terjadi kesalahan dalam memproses response');
                    console.log(response);
                }
            },
            error: function(xhr, status, error) {
                Swal.close();
                toastr.error('Terjadi kesalahan: ' + error);
            }
        });
    });

    // Reset form edit saat modal ditutup
    $('#modalEditDokumentasi').on('hidden.bs.modal', function() {
        $('#formEditDokumentasi')[0].reset();
        $('#editPreviewFoto').empty();
        $('#editVideoInputs').html(`
            <div class="video-input-group">
                <input type="url" class="form-control" name="video_baru[]" placeholder="https://youtube.com/watch?v=...">
            </div>
        `);
    });
});

// Fungsi untuk detail dokumentasi
function detailDokumentasi(id) {
    $.ajax({
        url: 'ajax/get_detail_dokumentasi.php',
        type: 'POST',
        data: { id: id },
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.status === 'success') {
                    showModalDetail(result.data);
                } else {
                    toastr.error(result.message);
                }
            } catch (e) {
                toastr.error('Terjadi kesalahan dalam memproses data');
            }
        },
        error: function() {
            toastr.error('Terjadi kesalahan dalam mengambil data');
        }
    });
}

// Fungsi untuk edit dokumentasi
function editDokumentasi(id) {
    $.ajax({
        url: 'ajax/get_dokumentasi.php',
        type: 'POST',
        data: { id: id },
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.status === 'success') {
                    showModalEdit(result.data);
                } else {
                    toastr.error(result.message);
                }
            } catch (e) {
                toastr.error('Terjadi kesalahan dalam memproses data');
            }
        },
        error: function() {
            toastr.error('Terjadi kesalahan dalam mengambil data');
        }
    });
}

// Fungsi untuk hapus dokumentasi
function hapusDokumentasi(id) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: 'Apakah Anda yakin ingin menghapus dokumentasi ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'process/hapus_dokumentasi.php',
                type: 'POST',
                data: { id: id },
                success: function(response) {
                    try {
                        var result = JSON.parse(response);
                        if (result.status === 'success') {
                            toastr.success(result.message);
                            $('#tableDokumentasi').DataTable().ajax.reload();
                        } else {
                            toastr.error(result.message);
                        }
                    } catch (e) {
                        toastr.error('Terjadi kesalahan dalam memproses response');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan dalam menghapus data');
                }
            });
        }
    });
}

// Fungsi untuk menampilkan modal detail
function showModalDetail(data) {
    var main = data.main;
    var files = data.files;

    // Set judul modal
    $('#detailModalTitle').text('Detail Dokumentasi - ' + main.judul);

    // Buat konten detail
    var content = `
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><td><strong>Tanggal Kegiatan:</strong></td><td>${main.tanggal}</td></tr>
                    <tr><td><strong>Kategori:</strong></td><td>${main.kategori}</td></tr>
                    <tr><td><strong>Lokasi:</strong></td><td>${main.lokasi}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><td><strong>Unit:</strong></td><td>${main.unit}</td></tr>
                    <tr><td><strong>Jenis Dokumentasi:</strong></td><td>${main.jenis_dok}</td></tr>
                    <tr><td><strong>Dibuat:</strong></td><td>${formatDateTime(main.created_at)}</td></tr>
                </table>
            </div>
        </div>
    `;

    if (main.keterangan) {
        content += `
            <div class="row">
                <div class="col-md-12">
                    <strong>Deskripsi:</strong><br>
                    <p>${main.keterangan}</p>
                </div>
            </div>
        `;
    }

    // Tabel file
    content += `
        <div class="row">
            <div class="col-md-12">
                <h5><strong>File Dokumentasi:</strong></h5>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="40%">Nama File</th>
                            <th width="15%">Tipe</th>
                            <th width="40%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    if (files.length > 0) {
        files.forEach(function(file, index) {
            var aksi = '';
            if (file.tipe_file === 'foto') {
                aksi = `
                    <button type="button" class="btn btn-info btn-sm" onclick="previewGambar('${file.lokasi_file}/${file.file_path}')" title="Preview">
                        <i class="fa fa-eye"></i> Preview
                    </button>
                    <button type="button" class="btn btn-success btn-sm" onclick="unduhGambar('${file.id}', true)" title="Unduh dengan Watermark">
                        <i class="fa fa-download"></i> Unduh
                    </button>
                `;
                // Tambahkan tombol download original untuk admin
                if (typeof hakAkses !== 'undefined' && hakAkses == '1') {
                    aksi += `
                        <button type="button" class="btn btn-warning btn-sm" onclick="unduhGambar('${file.id}', false)" title="Download Original">
                            <i class="fa fa-download"></i> Original
                        </button>
                    `;
                }
            } else if (file.tipe_file === 'video') {
                aksi = `
                    <a href="${file.file_path}" target="_blank" class="btn btn-primary btn-sm" title="Buka Video">
                        <i class="fa fa-play"></i> Buka Video
                    </a>
                `;
            }

            content += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${file.file_path}</td>
                    <td><span class="label label-${file.tipe_file === 'foto' ? 'success' : 'info'}">${file.tipe_file.toUpperCase()}</span></td>
                    <td>${aksi}</td>
                </tr>
            `;
        });
    } else {
        content += `
            <tr>
                <td colspan="4" class="text-center">Tidak ada file</td>
            </tr>
        `;
    }

    content += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    $('#detailContent').html(content);
    $('#modalDetailDokumentasi').modal('show');
}

// Fungsi untuk format datetime
function formatDateTime(datetime) {
    if (!datetime) return '-';
    var date = new Date(datetime);
    return date.toLocaleDateString('id-ID') + ' ' + date.toLocaleTimeString('id-ID');
}

// Fungsi untuk preview gambar
function previewGambar(imagePath) {
    var fullPath = '../../' + imagePath;
    $('#previewImage').attr('src', fullPath);
    $('#modalPreviewGambar').modal('show');
}

// Fungsi untuk unduh gambar
function unduhGambar(fileId, withWatermark) {
    var url = 'process/unduh_gambar.php?id=' + fileId + '&watermark=' + (withWatermark ? '1' : '0');
    window.open(url, '_blank');
}

// Fungsi untuk menampilkan modal edit
function showModalEdit(data) {
    var main = data.main;
    var files = data.files;
    var options = data.options;

    // Isi form data utama
    $('#editId').val(main.id);
    $('#editJudul').val(main.judul);
    $('#editTanggal').val(main.tanggal);
    $('#editLokasi').val(main.lokasi);
    $('#editKeterangan').val(main.keterangan);

    // Isi dropdown kategori
    var kategoriHtml = '<option value="">Pilih Kategori</option>';
    options.kategori.forEach(function(item) {
        var selected = item.id == main.id_kategori ? 'selected' : '';
        kategoriHtml += `<option value="${item.id}" ${selected}>${item.deskripsi}</option>`;
    });
    $('#editKategori').html(kategoriHtml);

    // Isi dropdown jenis dokumentasi
    var jenisHtml = '<option value="">Pilih Jenis</option>';
    options.jenis.forEach(function(item) {
        var selected = item.id == main.id_jenis_dok ? 'selected' : '';
        jenisHtml += `<option value="${item.id}" ${selected}>${item.deskripsi}</option>`;
    });
    $('#editJenisDok').html(jenisHtml);

    // Isi unit penyelenggara
    var unitHtml = '';
    if (options.unit.length > 0) {
        // Admin - dropdown
        unitHtml = '<select class="form-control" name="unit" required>';
        unitHtml += '<option value="">Pilih Unit</option>';
        options.unit.forEach(function(item) {
            var selected = item.id == main.unit ? 'selected' : '';
            unitHtml += `<option value="${item.id}" ${selected}>${item.unit}</option>`;
        });
        unitHtml += '</select>';
    } else {
        // User unit - readonly
        unitHtml = `
            <input type="text" class="form-control" value="${main.nama_unit}" readonly>
            <input type="hidden" name="unit" value="${main.unit}">
        `;
    }
    $('#editUnitContainer').html(unitHtml);

    // Isi file list
    var fileListHtml = '';
    if (files.length > 0) {
        fileListHtml = `
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th width="5%">No</th>
                        <th width="40%">Nama File</th>
                        <th width="15%">Tipe</th>
                        <th width="25%">Upload</th>
                        <th width="15%">Aksi</th>
                    </tr>
                </thead>
                <tbody>
        `;

        files.forEach(function(file, index) {
            var aksi = `
                <button type="button" class="btn btn-danger btn-sm" onclick="hapusFile(${file.id})" title="Hapus File">
                    <i class="fa fa-trash"></i>
                </button>
            `;

            fileListHtml += `
                <tr id="fileRow${file.id}">
                    <td>${index + 1}</td>
                    <td>${file.file_path}</td>
                    <td><span class="label label-${file.tipe_file === 'foto' ? 'success' : 'info'}">${file.tipe_file.toUpperCase()}</span></td>
                    <td>${formatDateTime(file.uploaded_at)}</td>
                    <td>${aksi}</td>
                </tr>
            `;
        });

        fileListHtml += '</tbody></table>';
    } else {
        fileListHtml = '<p class="text-muted">Tidak ada file existing</p>';
    }

    $('#editFileList').html(fileListHtml);

    // Reset form tambahan
    $('#editInputFoto').val('');
    $('#editPreviewFoto').empty();
    $('#editVideoInputs').html(`
        <div class="video-input-group">
            <input type="url" class="form-control" name="video_baru[]" placeholder="https://youtube.com/watch?v=...">
        </div>
    `);

    $('#modalEditDokumentasi').modal('show');
}

// Fungsi untuk hapus file
function hapusFile(fileId) {
    Swal.fire({
        title: 'Konfirmasi Hapus File',
        text: 'Apakah Anda yakin ingin menghapus file ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'process/hapus_file.php',
                type: 'POST',
                data: { id: fileId },
                success: function(response) {
                    try {
                        var result = JSON.parse(response);
                        if (result.status === 'success') {
                            toastr.success(result.message);
                            $('#fileRow' + fileId).fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            toastr.error(result.message);
                        }
                    } catch (e) {
                        toastr.error('Terjadi kesalahan dalam memproses response');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan dalam menghapus file');
                }
            });
        }
    });
}

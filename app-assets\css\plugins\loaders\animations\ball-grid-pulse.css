@-webkit-keyframes ball-grid-pulse {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0.7; }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }

@-moz-keyframes ball-grid-pulse {
  0% {
    -moz-transform: scale(1);
    transform: scale(1); }
  50% {
    -moz-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0.7; }
  100% {
    -moz-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }

@-o-keyframes ball-grid-pulse {
  0% {
    -o-transform: scale(1);
    transform: scale(1); }
  50% {
    -o-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0.7; }
  100% {
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }

@keyframes ball-grid-pulse {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.5);
    -moz-transform: scale(0.5);
    -o-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0.7; }
  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }

.ball-grid-pulse {
  width: 57px; }
  .ball-grid-pulse > div:nth-child(1) {
    -webkit-animation-delay: 0.35s;
    -moz-animation-delay: 0.35s;
    -o-animation-delay: 0.35s;
    animation-delay: 0.35s;
    -webkit-animation-duration: 1.51s;
    -moz-animation-duration: 1.51s;
    -o-animation-duration: 1.51s;
    animation-duration: 1.51s; }
  .ball-grid-pulse > div:nth-child(2) {
    -webkit-animation-delay: 0.17s;
    -moz-animation-delay: 0.17s;
    -o-animation-delay: 0.17s;
    animation-delay: 0.17s;
    -webkit-animation-duration: 1.16s;
    -moz-animation-duration: 1.16s;
    -o-animation-duration: 1.16s;
    animation-duration: 1.16s; }
  .ball-grid-pulse > div:nth-child(3) {
    -webkit-animation-delay: -0.08s;
    -moz-animation-delay: -0.08s;
    -o-animation-delay: -0.08s;
    animation-delay: -0.08s;
    -webkit-animation-duration: 1.44s;
    -moz-animation-duration: 1.44s;
    -o-animation-duration: 1.44s;
    animation-duration: 1.44s; }
  .ball-grid-pulse > div:nth-child(4) {
    -webkit-animation-delay: 0.22s;
    -moz-animation-delay: 0.22s;
    -o-animation-delay: 0.22s;
    animation-delay: 0.22s;
    -webkit-animation-duration: 0.71s;
    -moz-animation-duration: 0.71s;
    -o-animation-duration: 0.71s;
    animation-duration: 0.71s; }
  .ball-grid-pulse > div:nth-child(5) {
    -webkit-animation-delay: 0.43s;
    -moz-animation-delay: 0.43s;
    -o-animation-delay: 0.43s;
    animation-delay: 0.43s;
    -webkit-animation-duration: 1.45s;
    -moz-animation-duration: 1.45s;
    -o-animation-duration: 1.45s;
    animation-duration: 1.45s; }
  .ball-grid-pulse > div:nth-child(6) {
    -webkit-animation-delay: 0.41s;
    -moz-animation-delay: 0.41s;
    -o-animation-delay: 0.41s;
    animation-delay: 0.41s;
    -webkit-animation-duration: 1.05s;
    -moz-animation-duration: 1.05s;
    -o-animation-duration: 1.05s;
    animation-duration: 1.05s; }
  .ball-grid-pulse > div:nth-child(7) {
    -webkit-animation-delay: 0.63s;
    -moz-animation-delay: 0.63s;
    -o-animation-delay: 0.63s;
    animation-delay: 0.63s;
    -webkit-animation-duration: 1.03s;
    -moz-animation-duration: 1.03s;
    -o-animation-duration: 1.03s;
    animation-duration: 1.03s; }
  .ball-grid-pulse > div:nth-child(8) {
    -webkit-animation-delay: 0.46s;
    -moz-animation-delay: 0.46s;
    -o-animation-delay: 0.46s;
    animation-delay: 0.46s;
    -webkit-animation-duration: 1.54s;
    -moz-animation-duration: 1.54s;
    -o-animation-duration: 1.54s;
    animation-duration: 1.54s; }
  .ball-grid-pulse > div:nth-child(9) {
    -webkit-animation-delay: 0.5s;
    -moz-animation-delay: 0.5s;
    -o-animation-delay: 0.5s;
    animation-delay: 0.5s;
    -webkit-animation-duration: 0.96s;
    -moz-animation-duration: 0.96s;
    -o-animation-duration: 0.96s;
    animation-duration: 0.96s; }
  .ball-grid-pulse > div {
    background-color: #404E67;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    margin: 2px;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    display: inline-block;
    float: left;
    -webkit-animation-name: ball-grid-pulse;
    -moz-animation-name: ball-grid-pulse;
    -o-animation-name: ball-grid-pulse;
    animation-name: ball-grid-pulse;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    -o-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-delay: 0;
    -moz-animation-delay: 0;
    -o-animation-delay: 0;
    animation-delay: 0; }

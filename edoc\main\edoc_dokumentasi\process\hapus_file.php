<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

try {
    if (empty($_POST['id'])) {
        throw new Exception('ID file tidak ditemukan');
    }

    $fileId = intval($_POST['id']);

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND ed.UNIT = '$user'";
    }

    // Cek apakah file ada dan user memiliki akses
    $sqlCheck = "SELECT edd.*, ed.UNIT 
                 FROM edoc_dokumentasi_detail edd
                 INNER JOIN edoc_dokumentasi ed ON edd.ID_DOKUMENTASI = ed.ID
                 WHERE edd.ID = $fileId 
                 AND edd.STATUS = 1 
                 AND ed.STATUS = 1 
                 $whereAccess";

    $resultCheck = mysqli_query($koneksi, $sqlCheck);

    if (!$resultCheck || mysqli_num_rows($resultCheck) == 0) {
        throw new Exception('File tidak ditemukan atau Anda tidak memiliki akses');
    }

    // Update status file menjadi 0 (soft delete)
    $sqlUpdate = "UPDATE edoc_dokumentasi_detail SET STATUS = 0 WHERE ID = $fileId";
    
    if (!mysqli_query($koneksi, $sqlUpdate)) {
        throw new Exception('Gagal menghapus file: ' . mysqli_error($koneksi));
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'File berhasil dihapus'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>

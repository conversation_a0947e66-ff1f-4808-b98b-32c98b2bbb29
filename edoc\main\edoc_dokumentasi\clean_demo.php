<?php
include '../../config.php';

echo "<h2>Clean Demo Data - Dokumentasi Kegiatan</h2>";

if (isset($_GET['confirm']) && $_GET['confirm'] == 'yes') {
    echo "<h3>Menghapus Demo Data...</h3>";
    
    // Hapus data detail terlebih dahulu
    $sqlDetail = "DELETE FROM edoc_dokumentasi_detail WHERE ID_DOKUMENTASI IN (
        SELECT ID FROM edoc_dokumentasi WHERE CREATED_BY = '90901'
    )";
    
    if (mysqli_query($koneksi, $sqlDetail)) {
        $affectedDetail = mysqli_affected_rows($koneksi);
        echo "<p style='color: green;'>✓ Dihapus $affectedDetail file detail</p>";
    } else {
        echo "<p style='color: red;'>✗ Error menghapus detail: " . mysqli_error($koneksi) . "</p>";
    }
    
    // Hapus data utama
    $sqlMain = "DELETE FROM edoc_dokumentasi WHERE CREATED_BY = '90901'";
    
    if (mysqli_query($koneksi, $sqlMain)) {
        $affectedMain = mysqli_affected_rows($koneksi);
        echo "<p style='color: green;'>✓ Dihapus $affectedMain dokumentasi</p>";
    } else {
        echo "<p style='color: red;'>✗ Error menghapus dokumentasi: " . mysqli_error($koneksi) . "</p>";
    }
    
    echo "<p style='color: blue;'>Demo data berhasil dibersihkan!</p>";
    
} else {
    echo "<h3>Konfirmasi Penghapusan</h3>";
    echo "<p style='color: orange;'>⚠ Tindakan ini akan menghapus semua demo data dokumentasi yang dibuat dengan CREATED_BY = '90901'</p>";
    
    // Tampilkan data yang akan dihapus
    $preview = mysqli_query($koneksi, "
        SELECT ed.JUDUL, ed.TANGGAL, COUNT(edd.ID) as total_files
        FROM edoc_dokumentasi ed
        LEFT JOIN edoc_dokumentasi_detail edd ON ed.ID = edd.ID_DOKUMENTASI
        WHERE ed.CREATED_BY = '90901' AND ed.STATUS = 1
        GROUP BY ed.ID
        ORDER BY ed.CREATED_AT DESC
    ");
    
    if (mysqli_num_rows($preview) > 0) {
        echo "<h4>Data yang akan dihapus:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr><th>Judul</th><th>Tanggal</th><th>Total Files</th></tr>";
        
        while ($row = mysqli_fetch_array($preview)) {
            echo "<tr>";
            echo "<td>{$row['JUDUL']}</td>";
            echo "<td>{$row['TANGGAL']}</td>";
            echo "<td>{$row['total_files']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<br>";
        echo "<p><a href='?confirm=yes' style='background: red; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>YA, HAPUS SEMUA DEMO DATA</a></p>";
        echo "<p><a href='index.php' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>BATAL</a></p>";
        
    } else {
        echo "<p style='color: blue;'>Tidak ada demo data yang ditemukan untuk dihapus.</p>";
    }
}

echo "<hr>";
echo "<p><a href='index.php'>← Kembali ke Dokumentasi Kegiatan</a></p>";
echo "<p><a href='demo_data.php'>→ Insert Demo Data</a></p>";
?>

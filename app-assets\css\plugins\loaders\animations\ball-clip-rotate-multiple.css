@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-moz-keyframes rotate {
  0% {
    -moz-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -moz-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -moz-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@-o-keyframes rotate {
  0% {
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
    transform: rotate(0deg) scale(1); }
  50% {
    -webkit-transform: rotate(180deg) scale(0.6);
    -moz-transform: rotate(180deg) scale(0.6);
    -o-transform: rotate(180deg) scale(0.6);
    transform: rotate(180deg) scale(0.6); }
  100% {
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
    transform: rotate(360deg) scale(1); } }

.ball-clip-rotate-multiple {
  position: relative; }
  .ball-clip-rotate-multiple > div {
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    left: -20px;
    top: -20px;
    border: 2px solid #404E67;
    border-bottom-color: transparent;
    border-top-color: transparent;
    border-radius: 100%;
    height: 35px;
    width: 35px;
    -webkit-animation: rotate 1s 0s ease-in-out infinite;
    -moz-animation: rotate 1s 0s ease-in-out infinite;
    -o-animation: rotate 1s 0s ease-in-out infinite;
    animation: rotate 1s 0s ease-in-out infinite; }
    .ball-clip-rotate-multiple > div:last-child {
      display: inline-block;
      top: -10px;
      left: -10px;
      width: 15px;
      height: 15px;
      -webkit-animation-duration: 0.5s;
      -moz-animation-duration: 0.5s;
      -o-animation-duration: 0.5s;
      animation-duration: 0.5s;
      border-color: #404E67 transparent #404E67 transparent;
      animation-direction: reverse; }

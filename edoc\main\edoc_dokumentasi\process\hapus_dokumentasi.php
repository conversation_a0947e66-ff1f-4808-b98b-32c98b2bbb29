<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

try {
    if (empty($_POST['id'])) {
        throw new Exception('ID dokumentasi tidak ditemukan');
    }

    $id = intval($_POST['id']);

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND UNIT = '$user'";
    }

    // Cek apakah data ada dan user memiliki akses
    $sqlCheck = "SELECT ID FROM edoc_dokumentasi WHERE ID = $id AND STATUS = 1 $whereAccess";
    $resultCheck = mysqli_query($koneksi, $sqlCheck);

    if (!$resultCheck || mysqli_num_rows($resultCheck) == 0) {
        throw new Exception('Data dokumentasi tidak ditemukan atau Anda tidak memiliki akses');
    }

    // Update status menjadi 0 (soft delete)
    $sqlUpdate = "UPDATE edoc_dokumentasi SET STATUS = 0 WHERE ID = $id";
    
    if (!mysqli_query($koneksi, $sqlUpdate)) {
        throw new Exception('Gagal menghapus dokumentasi: ' . mysqli_error($koneksi));
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Dokumentasi berhasil dihapus'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>

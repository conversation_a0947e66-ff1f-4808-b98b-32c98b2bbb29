.bs-callout-white {
  display: block;
  border-color: #FFFFFF !important;
  background-color: white;
  border-radius: 0.25rem;
  color: gray; }
  .bs-callout-white h1, .bs-callout-white h2, .bs-callout-white h3, .bs-callout-white h4, .bs-callout-white h5, .bs-callout-white h6 {
    margin-top: 0;
    color: #FFFFFF; }
  .bs-callout-white p:last-child {
    margin-bottom: 0; }
  .bs-callout-white code, .bs-callout-white .highlight {
    background-color: #fff; }
  .bs-callout-white.callout-transparent {
    display: block;
    border-color: #FFFFFF !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: gray; }
    .bs-callout-white.callout-transparent h1, .bs-callout-white.callout-transparent h2, .bs-callout-white.callout-transparent h3, .bs-callout-white.callout-transparent h4, .bs-callout-white.callout-transparent h5, .bs-callout-white.callout-transparent h6 {
      margin-top: 0;
      color: #FFFFFF; }
    .bs-callout-white.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-white.callout-transparent code, .bs-callout-white.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-white .callout-arrow-left:before {
    border-left-color: #FFFFFF; }
  .bs-callout-white .callout-arrow-right:before {
    border-right-color: #FFFFFF; }

.bs-callout-black {
  display: block;
  border-color: #000000 !important;
  background-color: #525252;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-black h1, .bs-callout-black h2, .bs-callout-black h3, .bs-callout-black h4, .bs-callout-black h5, .bs-callout-black h6 {
    margin-top: 0;
    color: #000000; }
  .bs-callout-black p:last-child {
    margin-bottom: 0; }
  .bs-callout-black code, .bs-callout-black .highlight {
    background-color: #fff; }
  .bs-callout-black.callout-transparent {
    display: block;
    border-color: #000000 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-black.callout-transparent h1, .bs-callout-black.callout-transparent h2, .bs-callout-black.callout-transparent h3, .bs-callout-black.callout-transparent h4, .bs-callout-black.callout-transparent h5, .bs-callout-black.callout-transparent h6 {
      margin-top: 0;
      color: #000000; }
    .bs-callout-black.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-black.callout-transparent code, .bs-callout-black.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-black .callout-arrow-left:before {
    border-left-color: #000000; }
  .bs-callout-black .callout-arrow-right:before {
    border-right-color: #000000; }

.climacon-primary.climacon-lighten-5 .climacon_component-stroke {
  fill: #E0F6F6; }

.climacon-primary.climacon-lighten-4 .climacon_component-stroke {
  fill: #B3E9EA; }

.climacon-primary.climacon-lighten-3 .climacon_component-stroke {
  fill: #80DADC; }

.climacon-primary.climacon-lighten-2 .climacon_component-stroke {
  fill: #4DCBCD; }

.climacon-primary.climacon-lighten-1 .climacon_component-stroke {
  fill: #26C0C3; }

.bs-callout-primary {
  display: block;
  border-color: #00B5B8 !important;
  background-color: #5cfcff;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-primary h1, .bs-callout-primary h2, .bs-callout-primary h3, .bs-callout-primary h4, .bs-callout-primary h5, .bs-callout-primary h6 {
    margin-top: 0;
    color: #00B5B8; }
  .bs-callout-primary p:last-child {
    margin-bottom: 0; }
  .bs-callout-primary code, .bs-callout-primary .highlight {
    background-color: #fff; }
  .bs-callout-primary.callout-transparent {
    display: block;
    border-color: #00B5B8 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-primary.callout-transparent h1, .bs-callout-primary.callout-transparent h2, .bs-callout-primary.callout-transparent h3, .bs-callout-primary.callout-transparent h4, .bs-callout-primary.callout-transparent h5, .bs-callout-primary.callout-transparent h6 {
      margin-top: 0;
      color: #00B5B8; }
    .bs-callout-primary.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-primary.callout-transparent code, .bs-callout-primary.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-primary .callout-arrow-left:before {
    border-left-color: #00B5B8; }
  .bs-callout-primary .callout-arrow-right:before {
    border-right-color: #00B5B8; }

.climacon-primary.climacon-darken-1 .climacon_component-stroke {
  fill: #00AEB1; }

.climacon-primary.climacon-darken-2 .climacon_component-stroke {
  fill: #00A5A8; }

.climacon-primary.climacon-darken-3 .climacon_component-stroke {
  fill: #009DA0; }

.climacon-primary.climacon-darken-4 .climacon_component-stroke {
  fill: #008D91; }

.climacon-primary.climacon-accent-1 .climacon_component-stroke {
  fill: #BDFDFF; }

.climacon-primary.climacon-accent-2 .climacon_component-stroke {
  fill: #8AFBFF; }

.climacon-primary.climacon-accent-3 .climacon_component-stroke {
  fill: #57FAFF; }

.climacon-primary.climacon-accent-4 .climacon_component-stroke {
  fill: #3DF9FF; }

.climacon-success.climacon-lighten-5 .climacon_component-stroke {
  fill: #E3FAF3; }

.climacon-success.climacon-lighten-4 .climacon_component-stroke {
  fill: #B9F2E1; }

.climacon-success.climacon-lighten-3 .climacon_component-stroke {
  fill: #8BE9CD; }

.climacon-success.climacon-lighten-2 .climacon_component-stroke {
  fill: #5CE0B8; }

.climacon-success.climacon-lighten-1 .climacon_component-stroke {
  fill: #39DAA9; }

.bs-callout-success {
  display: block;
  border-color: #16D39A !important;
  background-color: #98f4d8;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-success h1, .bs-callout-success h2, .bs-callout-success h3, .bs-callout-success h4, .bs-callout-success h5, .bs-callout-success h6 {
    margin-top: 0;
    color: #16D39A; }
  .bs-callout-success p:last-child {
    margin-bottom: 0; }
  .bs-callout-success code, .bs-callout-success .highlight {
    background-color: #fff; }
  .bs-callout-success.callout-transparent {
    display: block;
    border-color: #16D39A !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-success.callout-transparent h1, .bs-callout-success.callout-transparent h2, .bs-callout-success.callout-transparent h3, .bs-callout-success.callout-transparent h4, .bs-callout-success.callout-transparent h5, .bs-callout-success.callout-transparent h6 {
      margin-top: 0;
      color: #16D39A; }
    .bs-callout-success.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-success.callout-transparent code, .bs-callout-success.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-success .callout-arrow-left:before {
    border-left-color: #16D39A; }
  .bs-callout-success .callout-arrow-right:before {
    border-right-color: #16D39A; }

.climacon-success.climacon-darken-1 .climacon_component-stroke {
  fill: #13CE92; }

.climacon-success.climacon-darken-2 .climacon_component-stroke {
  fill: #10C888; }

.climacon-success.climacon-darken-3 .climacon_component-stroke {
  fill: #0CC27E; }

.climacon-success.climacon-darken-4 .climacon_component-stroke {
  fill: #06B76C; }

.climacon-success.climacon-accent-1 .climacon_component-stroke {
  fill: #E1FFF1; }

.climacon-success.climacon-accent-2 .climacon_component-stroke {
  fill: #AEFFD9; }

.climacon-success.climacon-accent-3 .climacon_component-stroke {
  fill: #7BFFC1; }

.climacon-success.climacon-accent-4 .climacon_component-stroke {
  fill: #62FFB5; }

.climacon-info.climacon-lighten-5 .climacon_component-stroke {
  fill: #E6F9FC; }

.climacon-info.climacon-lighten-4 .climacon_component-stroke {
  fill: #C0F0F7; }

.climacon-info.climacon-lighten-3 .climacon_component-stroke {
  fill: #96E7F1; }

.climacon-info.climacon-lighten-2 .climacon_component-stroke {
  fill: #6CDDEB; }

.climacon-info.climacon-lighten-1 .climacon_component-stroke {
  fill: #4DD5E7; }

.bs-callout-info {
  display: block;
  border-color: #2DCEE3 !important;
  background-color: #bdf0f6;
  border-radius: 0.25rem;
  color: #020e0f; }
  .bs-callout-info h1, .bs-callout-info h2, .bs-callout-info h3, .bs-callout-info h4, .bs-callout-info h5, .bs-callout-info h6 {
    margin-top: 0;
    color: #2DCEE3; }
  .bs-callout-info p:last-child {
    margin-bottom: 0; }
  .bs-callout-info code, .bs-callout-info .highlight {
    background-color: #fff; }
  .bs-callout-info.callout-transparent {
    display: block;
    border-color: #2DCEE3 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #020e0f; }
    .bs-callout-info.callout-transparent h1, .bs-callout-info.callout-transparent h2, .bs-callout-info.callout-transparent h3, .bs-callout-info.callout-transparent h4, .bs-callout-info.callout-transparent h5, .bs-callout-info.callout-transparent h6 {
      margin-top: 0;
      color: #2DCEE3; }
    .bs-callout-info.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-info.callout-transparent code, .bs-callout-info.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-info .callout-arrow-left:before {
    border-left-color: #2DCEE3; }
  .bs-callout-info .callout-arrow-right:before {
    border-right-color: #2DCEE3; }

.climacon-info.climacon-darken-1 .climacon_component-stroke {
  fill: #28C9E0; }

.climacon-info.climacon-darken-2 .climacon_component-stroke {
  fill: #22C2DC; }

.climacon-info.climacon-darken-3 .climacon_component-stroke {
  fill: #1CBCD8; }

.climacon-info.climacon-darken-4 .climacon_component-stroke {
  fill: #11B0D0; }

.climacon-info.climacon-accent-1 .climacon_component-stroke {
  fill: #FEFFFF; }

.climacon-info.climacon-accent-2 .climacon_component-stroke {
  fill: #CBF5FF; }

.climacon-info.climacon-accent-3 .climacon_component-stroke {
  fill: #98ECFF; }

.climacon-info.climacon-accent-4 .climacon_component-stroke {
  fill: #7FE7FF; }

.climacon-warning.climacon-lighten-5 .climacon_component-stroke {
  fill: #FFF5EF; }

.climacon-warning.climacon-lighten-4 .climacon_component-stroke {
  fill: #FFE5D8; }

.climacon-warning.climacon-lighten-3 .climacon_component-stroke {
  fill: #FFD4BE; }

.climacon-warning.climacon-lighten-2 .climacon_component-stroke {
  fill: #FFC2A4; }

.climacon-warning.climacon-lighten-1 .climacon_component-stroke {
  fill: #FFB591; }

.bs-callout-warning {
  display: block;
  border-color: #FFA87D !important;
  background-color: white;
  border-radius: 0.25rem;
  color: #7d2900; }
  .bs-callout-warning h1, .bs-callout-warning h2, .bs-callout-warning h3, .bs-callout-warning h4, .bs-callout-warning h5, .bs-callout-warning h6 {
    margin-top: 0;
    color: #FFA87D; }
  .bs-callout-warning p:last-child {
    margin-bottom: 0; }
  .bs-callout-warning code, .bs-callout-warning .highlight {
    background-color: #fff; }
  .bs-callout-warning.callout-transparent {
    display: block;
    border-color: #FFA87D !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #7d2900; }
    .bs-callout-warning.callout-transparent h1, .bs-callout-warning.callout-transparent h2, .bs-callout-warning.callout-transparent h3, .bs-callout-warning.callout-transparent h4, .bs-callout-warning.callout-transparent h5, .bs-callout-warning.callout-transparent h6 {
      margin-top: 0;
      color: #FFA87D; }
    .bs-callout-warning.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-warning.callout-transparent code, .bs-callout-warning.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-warning .callout-arrow-left:before {
    border-left-color: #FFA87D; }
  .bs-callout-warning .callout-arrow-right:before {
    border-right-color: #FFA87D; }

.climacon-warning.climacon-darken-1 .climacon_component-stroke {
  fill: #FFA075; }

.climacon-warning.climacon-darken-2 .climacon_component-stroke {
  fill: #FF976A; }

.climacon-warning.climacon-darken-3 .climacon_component-stroke {
  fill: #FF8D60; }

.climacon-warning.climacon-darken-4 .climacon_component-stroke {
  fill: #FF7D4D; }

.climacon-warning.climacon-accent-1 .climacon_component-stroke {
  fill: #FFF5EF; }

.climacon-warning.climacon-accent-2 .climacon_component-stroke {
  fill: #FFE5D8; }

.climacon-warning.climacon-accent-3 .climacon_component-stroke {
  fill: #FFF6F3; }

.climacon-warning.climacon-accent-4 .climacon_component-stroke {
  fill: #FFE3DA; }

.climacon-danger.climacon-lighten-5 .climacon_component-stroke {
  fill: #FFEEF1; }

.climacon-danger.climacon-lighten-4 .climacon_component-stroke {
  fill: #FFD6DB; }

.climacon-danger.climacon-lighten-3 .climacon_component-stroke {
  fill: #FFBAC4; }

.climacon-danger.climacon-lighten-2 .climacon_component-stroke {
  fill: #FF9EAC; }

.climacon-danger.climacon-lighten-1 .climacon_component-stroke {
  fill: #FF8A9A; }

.bs-callout-danger {
  display: block;
  border-color: #FF7588 !important;
  background-color: white;
  border-radius: 0.25rem;
  color: #750010; }
  .bs-callout-danger h1, .bs-callout-danger h2, .bs-callout-danger h3, .bs-callout-danger h4, .bs-callout-danger h5, .bs-callout-danger h6 {
    margin-top: 0;
    color: #FF7588; }
  .bs-callout-danger p:last-child {
    margin-bottom: 0; }
  .bs-callout-danger code, .bs-callout-danger .highlight {
    background-color: #fff; }
  .bs-callout-danger.callout-transparent {
    display: block;
    border-color: #FF7588 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #750010; }
    .bs-callout-danger.callout-transparent h1, .bs-callout-danger.callout-transparent h2, .bs-callout-danger.callout-transparent h3, .bs-callout-danger.callout-transparent h4, .bs-callout-danger.callout-transparent h5, .bs-callout-danger.callout-transparent h6 {
      margin-top: 0;
      color: #FF7588; }
    .bs-callout-danger.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-danger.callout-transparent code, .bs-callout-danger.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-danger .callout-arrow-left:before {
    border-left-color: #FF7588; }
  .bs-callout-danger .callout-arrow-right:before {
    border-right-color: #FF7588; }

.climacon-danger.climacon-darken-1 .climacon_component-stroke {
  fill: #FF6D80; }

.climacon-danger.climacon-darken-2 .climacon_component-stroke {
  fill: #FF6275; }

.climacon-danger.climacon-darken-3 .climacon_component-stroke {
  fill: #FF586B; }

.climacon-danger.climacon-darken-4 .climacon_component-stroke {
  fill: #FF4558; }

.climacon-danger.climacon-accent-1 .climacon_component-stroke {
  fill: #FFEEF1; }

.climacon-danger.climacon-accent-2 .climacon_component-stroke {
  fill: #FFD6DB; }

.climacon-danger.climacon-accent-3 .climacon_component-stroke {
  fill: #FFECEE; }

.climacon-danger.climacon-accent-4 .climacon_component-stroke {
  fill: #FFD3D7; }

.climacon-red.climacon-lighten-5 .climacon_component-stroke {
  fill: #FFEBEE; }

.climacon-red.climacon-lighten-4 .climacon_component-stroke {
  fill: #FFCDD2; }

.climacon-red.climacon-lighten-3 .climacon_component-stroke {
  fill: #EF9A9A; }

.climacon-red.climacon-lighten-2 .climacon_component-stroke {
  fill: #E57373; }

.climacon-red.climacon-lighten-1 .climacon_component-stroke {
  fill: #EF5350; }

.bs-callout-red {
  display: block;
  border-color: #F44336 !important;
  background-color: #fcd4d1;
  border-radius: 0.25rem;
  color: #290502; }
  .bs-callout-red h1, .bs-callout-red h2, .bs-callout-red h3, .bs-callout-red h4, .bs-callout-red h5, .bs-callout-red h6 {
    margin-top: 0;
    color: #F44336; }
  .bs-callout-red p:last-child {
    margin-bottom: 0; }
  .bs-callout-red code, .bs-callout-red .highlight {
    background-color: #fff; }
  .bs-callout-red.callout-transparent {
    display: block;
    border-color: #F44336 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #290502; }
    .bs-callout-red.callout-transparent h1, .bs-callout-red.callout-transparent h2, .bs-callout-red.callout-transparent h3, .bs-callout-red.callout-transparent h4, .bs-callout-red.callout-transparent h5, .bs-callout-red.callout-transparent h6 {
      margin-top: 0;
      color: #F44336; }
    .bs-callout-red.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-red.callout-transparent code, .bs-callout-red.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-red .callout-arrow-left:before {
    border-left-color: #F44336; }
  .bs-callout-red .callout-arrow-right:before {
    border-right-color: #F44336; }

.climacon-red.climacon-darken-1 .climacon_component-stroke {
  fill: #E53935; }

.climacon-red.climacon-darken-2 .climacon_component-stroke {
  fill: #D32F2F; }

.climacon-red.climacon-darken-3 .climacon_component-stroke {
  fill: #C62828; }

.climacon-red.climacon-darken-4 .climacon_component-stroke {
  fill: #B71C1C; }

.climacon-red.climacon-accent-1 .climacon_component-stroke {
  fill: #FF8A80; }

.climacon-red.climacon-accent-2 .climacon_component-stroke {
  fill: #FF5252; }

.climacon-red.climacon-accent-3 .climacon_component-stroke {
  fill: #FF1744; }

.climacon-red.climacon-accent-4 .climacon_component-stroke {
  fill: #D50000; }

.climacon-pink.climacon-lighten-5 .climacon_component-stroke {
  fill: #FCE4EC; }

.climacon-pink.climacon-lighten-4 .climacon_component-stroke {
  fill: #F8BBD0; }

.climacon-pink.climacon-lighten-3 .climacon_component-stroke {
  fill: #F48FB1; }

.climacon-pink.climacon-lighten-2 .climacon_component-stroke {
  fill: #F06292; }

.climacon-pink.climacon-lighten-1 .climacon_component-stroke {
  fill: #EC407A; }

.bs-callout-pink {
  display: block;
  border-color: #E91E63 !important;
  background-color: #f8b3ca;
  border-radius: 0.25rem;
  color: #070103; }
  .bs-callout-pink h1, .bs-callout-pink h2, .bs-callout-pink h3, .bs-callout-pink h4, .bs-callout-pink h5, .bs-callout-pink h6 {
    margin-top: 0;
    color: #E91E63; }
  .bs-callout-pink p:last-child {
    margin-bottom: 0; }
  .bs-callout-pink code, .bs-callout-pink .highlight {
    background-color: #fff; }
  .bs-callout-pink.callout-transparent {
    display: block;
    border-color: #E91E63 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #070103; }
    .bs-callout-pink.callout-transparent h1, .bs-callout-pink.callout-transparent h2, .bs-callout-pink.callout-transparent h3, .bs-callout-pink.callout-transparent h4, .bs-callout-pink.callout-transparent h5, .bs-callout-pink.callout-transparent h6 {
      margin-top: 0;
      color: #E91E63; }
    .bs-callout-pink.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-pink.callout-transparent code, .bs-callout-pink.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-pink .callout-arrow-left:before {
    border-left-color: #E91E63; }
  .bs-callout-pink .callout-arrow-right:before {
    border-right-color: #E91E63; }

.climacon-pink.climacon-darken-1 .climacon_component-stroke {
  fill: #D81B60; }

.climacon-pink.climacon-darken-2 .climacon_component-stroke {
  fill: #C2185B; }

.climacon-pink.climacon-darken-3 .climacon_component-stroke {
  fill: #AD1457; }

.climacon-pink.climacon-darken-4 .climacon_component-stroke {
  fill: #880E4F; }

.climacon-pink.climacon-accent-1 .climacon_component-stroke {
  fill: #FF80AB; }

.climacon-pink.climacon-accent-2 .climacon_component-stroke {
  fill: #FF4081; }

.climacon-pink.climacon-accent-3 .climacon_component-stroke {
  fill: #F50057; }

.climacon-pink.climacon-accent-4 .climacon_component-stroke {
  fill: #C51162; }

.climacon-purple.climacon-lighten-5 .climacon_component-stroke {
  fill: #F3E5F5; }

.climacon-purple.climacon-lighten-4 .climacon_component-stroke {
  fill: #E1BEE7; }

.climacon-purple.climacon-lighten-3 .climacon_component-stroke {
  fill: #CE93D8; }

.climacon-purple.climacon-lighten-2 .climacon_component-stroke {
  fill: #BA68C8; }

.climacon-purple.climacon-lighten-1 .climacon_component-stroke {
  fill: #AB47BC; }

.bs-callout-purple {
  display: block;
  border-color: #9C27B0 !important;
  background-color: #db93e7;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-purple h1, .bs-callout-purple h2, .bs-callout-purple h3, .bs-callout-purple h4, .bs-callout-purple h5, .bs-callout-purple h6 {
    margin-top: 0;
    color: #9C27B0; }
  .bs-callout-purple p:last-child {
    margin-bottom: 0; }
  .bs-callout-purple code, .bs-callout-purple .highlight {
    background-color: #fff; }
  .bs-callout-purple.callout-transparent {
    display: block;
    border-color: #9C27B0 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-purple.callout-transparent h1, .bs-callout-purple.callout-transparent h2, .bs-callout-purple.callout-transparent h3, .bs-callout-purple.callout-transparent h4, .bs-callout-purple.callout-transparent h5, .bs-callout-purple.callout-transparent h6 {
      margin-top: 0;
      color: #9C27B0; }
    .bs-callout-purple.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-purple.callout-transparent code, .bs-callout-purple.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-purple .callout-arrow-left:before {
    border-left-color: #9C27B0; }
  .bs-callout-purple .callout-arrow-right:before {
    border-right-color: #9C27B0; }

.climacon-purple.climacon-darken-1 .climacon_component-stroke {
  fill: #8E24AA; }

.climacon-purple.climacon-darken-2 .climacon_component-stroke {
  fill: #7B1FA2; }

.climacon-purple.climacon-darken-3 .climacon_component-stroke {
  fill: #6A1B9A; }

.climacon-purple.climacon-darken-4 .climacon_component-stroke {
  fill: #4A148C; }

.climacon-purple.climacon-accent-1 .climacon_component-stroke {
  fill: #EA80FC; }

.climacon-purple.climacon-accent-2 .climacon_component-stroke {
  fill: #E040FB; }

.climacon-purple.climacon-accent-3 .climacon_component-stroke {
  fill: #D500F9; }

.climacon-purple.climacon-accent-4 .climacon_component-stroke {
  fill: #DD00FF; }

.climacon-blue.climacon-lighten-5 .climacon_component-stroke {
  fill: #E3F2FD; }

.climacon-blue.climacon-lighten-4 .climacon_component-stroke {
  fill: #BBDEFB; }

.climacon-blue.climacon-lighten-3 .climacon_component-stroke {
  fill: #90CAF9; }

.climacon-blue.climacon-lighten-2 .climacon_component-stroke {
  fill: #64B5F6; }

.climacon-blue.climacon-lighten-1 .climacon_component-stroke {
  fill: #42A5F5; }

.bs-callout-blue {
  display: block;
  border-color: #2196F3 !important;
  background-color: #bcdffb;
  border-radius: 0.25rem;
  color: #010c14; }
  .bs-callout-blue h1, .bs-callout-blue h2, .bs-callout-blue h3, .bs-callout-blue h4, .bs-callout-blue h5, .bs-callout-blue h6 {
    margin-top: 0;
    color: #2196F3; }
  .bs-callout-blue p:last-child {
    margin-bottom: 0; }
  .bs-callout-blue code, .bs-callout-blue .highlight {
    background-color: #fff; }
  .bs-callout-blue.callout-transparent {
    display: block;
    border-color: #2196F3 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #010c14; }
    .bs-callout-blue.callout-transparent h1, .bs-callout-blue.callout-transparent h2, .bs-callout-blue.callout-transparent h3, .bs-callout-blue.callout-transparent h4, .bs-callout-blue.callout-transparent h5, .bs-callout-blue.callout-transparent h6 {
      margin-top: 0;
      color: #2196F3; }
    .bs-callout-blue.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-blue.callout-transparent code, .bs-callout-blue.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-blue .callout-arrow-left:before {
    border-left-color: #2196F3; }
  .bs-callout-blue .callout-arrow-right:before {
    border-right-color: #2196F3; }

.climacon-blue.climacon-darken-1 .climacon_component-stroke {
  fill: #1E88E5; }

.climacon-blue.climacon-darken-2 .climacon_component-stroke {
  fill: #1976D2; }

.climacon-blue.climacon-darken-3 .climacon_component-stroke {
  fill: #1565C0; }

.climacon-blue.climacon-darken-4 .climacon_component-stroke {
  fill: #0D47A1; }

.climacon-blue.climacon-accent-1 .climacon_component-stroke {
  fill: #82B1FF; }

.climacon-blue.climacon-accent-2 .climacon_component-stroke {
  fill: #448AFF; }

.climacon-blue.climacon-accent-3 .climacon_component-stroke {
  fill: #2979FF; }

.climacon-blue.climacon-accent-4 .climacon_component-stroke {
  fill: #2962FF; }

.climacon-cyan.climacon-lighten-5 .climacon_component-stroke {
  fill: #E0F7FA; }

.climacon-cyan.climacon-lighten-4 .climacon_component-stroke {
  fill: #B2EBF2; }

.climacon-cyan.climacon-lighten-3 .climacon_component-stroke {
  fill: #80DEEA; }

.climacon-cyan.climacon-lighten-2 .climacon_component-stroke {
  fill: #4DD0E1; }

.climacon-cyan.climacon-lighten-1 .climacon_component-stroke {
  fill: #26C6DA; }

.bs-callout-cyan {
  display: block;
  border-color: #00BCD4 !important;
  background-color: #78f0ff;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-cyan h1, .bs-callout-cyan h2, .bs-callout-cyan h3, .bs-callout-cyan h4, .bs-callout-cyan h5, .bs-callout-cyan h6 {
    margin-top: 0;
    color: #00BCD4; }
  .bs-callout-cyan p:last-child {
    margin-bottom: 0; }
  .bs-callout-cyan code, .bs-callout-cyan .highlight {
    background-color: #fff; }
  .bs-callout-cyan.callout-transparent {
    display: block;
    border-color: #00BCD4 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-cyan.callout-transparent h1, .bs-callout-cyan.callout-transparent h2, .bs-callout-cyan.callout-transparent h3, .bs-callout-cyan.callout-transparent h4, .bs-callout-cyan.callout-transparent h5, .bs-callout-cyan.callout-transparent h6 {
      margin-top: 0;
      color: #00BCD4; }
    .bs-callout-cyan.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-cyan.callout-transparent code, .bs-callout-cyan.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-cyan .callout-arrow-left:before {
    border-left-color: #00BCD4; }
  .bs-callout-cyan .callout-arrow-right:before {
    border-right-color: #00BCD4; }

.climacon-cyan.climacon-darken-1 .climacon_component-stroke {
  fill: #00ACC1; }

.climacon-cyan.climacon-darken-2 .climacon_component-stroke {
  fill: #0097A7; }

.climacon-cyan.climacon-darken-3 .climacon_component-stroke {
  fill: #00838F; }

.climacon-cyan.climacon-darken-4 .climacon_component-stroke {
  fill: #006064; }

.climacon-cyan.climacon-accent-1 .climacon_component-stroke {
  fill: #84FFFF; }

.climacon-cyan.climacon-accent-2 .climacon_component-stroke {
  fill: #18FFFF; }

.climacon-cyan.climacon-accent-3 .climacon_component-stroke {
  fill: #00E5FF; }

.climacon-cyan.climacon-accent-4 .climacon_component-stroke {
  fill: #00B8D4; }

.climacon-teal.climacon-lighten-5 .climacon_component-stroke {
  fill: #E0F2F1; }

.climacon-teal.climacon-lighten-4 .climacon_component-stroke {
  fill: #B2DFDB; }

.climacon-teal.climacon-lighten-3 .climacon_component-stroke {
  fill: #80CBC4; }

.climacon-teal.climacon-lighten-2 .climacon_component-stroke {
  fill: #4DB6AC; }

.climacon-teal.climacon-lighten-1 .climacon_component-stroke {
  fill: #26A69A; }

.bs-callout-teal {
  display: block;
  border-color: #009688 !important;
  background-color: #3affed;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-teal h1, .bs-callout-teal h2, .bs-callout-teal h3, .bs-callout-teal h4, .bs-callout-teal h5, .bs-callout-teal h6 {
    margin-top: 0;
    color: #009688; }
  .bs-callout-teal p:last-child {
    margin-bottom: 0; }
  .bs-callout-teal code, .bs-callout-teal .highlight {
    background-color: #fff; }
  .bs-callout-teal.callout-transparent {
    display: block;
    border-color: #009688 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-teal.callout-transparent h1, .bs-callout-teal.callout-transparent h2, .bs-callout-teal.callout-transparent h3, .bs-callout-teal.callout-transparent h4, .bs-callout-teal.callout-transparent h5, .bs-callout-teal.callout-transparent h6 {
      margin-top: 0;
      color: #009688; }
    .bs-callout-teal.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-teal.callout-transparent code, .bs-callout-teal.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-teal .callout-arrow-left:before {
    border-left-color: #009688; }
  .bs-callout-teal .callout-arrow-right:before {
    border-right-color: #009688; }

.climacon-teal.climacon-darken-1 .climacon_component-stroke {
  fill: #00897B; }

.climacon-teal.climacon-darken-2 .climacon_component-stroke {
  fill: #00796B; }

.climacon-teal.climacon-darken-3 .climacon_component-stroke {
  fill: #00695C; }

.climacon-teal.climacon-darken-4 .climacon_component-stroke {
  fill: #004D40; }

.climacon-teal.climacon-accent-1 .climacon_component-stroke {
  fill: #A7FFEB; }

.climacon-teal.climacon-accent-2 .climacon_component-stroke {
  fill: #64FFDA; }

.climacon-teal.climacon-accent-3 .climacon_component-stroke {
  fill: #1DE9B6; }

.climacon-teal.climacon-accent-4 .climacon_component-stroke {
  fill: #00BFA5; }

.climacon-yellow.climacon-lighten-5 .climacon_component-stroke {
  fill: #FFFDE7; }

.climacon-yellow.climacon-lighten-4 .climacon_component-stroke {
  fill: #FFF9C4; }

.climacon-yellow.climacon-lighten-3 .climacon_component-stroke {
  fill: #FFF59D; }

.climacon-yellow.climacon-lighten-2 .climacon_component-stroke {
  fill: #FFF176; }

.climacon-yellow.climacon-lighten-1 .climacon_component-stroke {
  fill: #FFEE58; }

.bs-callout-yellow {
  display: block;
  border-color: #FFEB3B !important;
  background-color: #fffcde;
  border-radius: 0.25rem;
  color: #3b3500; }
  .bs-callout-yellow h1, .bs-callout-yellow h2, .bs-callout-yellow h3, .bs-callout-yellow h4, .bs-callout-yellow h5, .bs-callout-yellow h6 {
    margin-top: 0;
    color: #FFEB3B; }
  .bs-callout-yellow p:last-child {
    margin-bottom: 0; }
  .bs-callout-yellow code, .bs-callout-yellow .highlight {
    background-color: #fff; }
  .bs-callout-yellow.callout-transparent {
    display: block;
    border-color: #FFEB3B !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #3b3500; }
    .bs-callout-yellow.callout-transparent h1, .bs-callout-yellow.callout-transparent h2, .bs-callout-yellow.callout-transparent h3, .bs-callout-yellow.callout-transparent h4, .bs-callout-yellow.callout-transparent h5, .bs-callout-yellow.callout-transparent h6 {
      margin-top: 0;
      color: #FFEB3B; }
    .bs-callout-yellow.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-yellow.callout-transparent code, .bs-callout-yellow.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-yellow .callout-arrow-left:before {
    border-left-color: #FFEB3B; }
  .bs-callout-yellow .callout-arrow-right:before {
    border-right-color: #FFEB3B; }

.climacon-yellow.climacon-darken-1 .climacon_component-stroke {
  fill: #FDD835; }

.climacon-yellow.climacon-darken-2 .climacon_component-stroke {
  fill: #FBC02D; }

.climacon-yellow.climacon-darken-3 .climacon_component-stroke {
  fill: #F9A825; }

.climacon-yellow.climacon-darken-4 .climacon_component-stroke {
  fill: #F57F17; }

.climacon-yellow.climacon-accent-1 .climacon_component-stroke {
  fill: #FFFF8D; }

.climacon-yellow.climacon-accent-2 .climacon_component-stroke {
  fill: #FFFF00; }

.climacon-yellow.climacon-accent-3 .climacon_component-stroke {
  fill: #FFEA00; }

.climacon-yellow.climacon-accent-4 .climacon_component-stroke {
  fill: #FFD600; }

.climacon-amber.climacon-lighten-5 .climacon_component-stroke {
  fill: #FFF8E1; }

.climacon-amber.climacon-lighten-4 .climacon_component-stroke {
  fill: #FFECB3; }

.climacon-amber.climacon-lighten-3 .climacon_component-stroke {
  fill: #FFE082; }

.climacon-amber.climacon-lighten-2 .climacon_component-stroke {
  fill: #FFD54F; }

.climacon-amber.climacon-lighten-1 .climacon_component-stroke {
  fill: #FFCA28; }

.bs-callout-amber {
  display: block;
  border-color: #FFC107 !important;
  background-color: #ffeaaa;
  border-radius: 0.25rem;
  color: #070500; }
  .bs-callout-amber h1, .bs-callout-amber h2, .bs-callout-amber h3, .bs-callout-amber h4, .bs-callout-amber h5, .bs-callout-amber h6 {
    margin-top: 0;
    color: #FFC107; }
  .bs-callout-amber p:last-child {
    margin-bottom: 0; }
  .bs-callout-amber code, .bs-callout-amber .highlight {
    background-color: #fff; }
  .bs-callout-amber.callout-transparent {
    display: block;
    border-color: #FFC107 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: #070500; }
    .bs-callout-amber.callout-transparent h1, .bs-callout-amber.callout-transparent h2, .bs-callout-amber.callout-transparent h3, .bs-callout-amber.callout-transparent h4, .bs-callout-amber.callout-transparent h5, .bs-callout-amber.callout-transparent h6 {
      margin-top: 0;
      color: #FFC107; }
    .bs-callout-amber.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-amber.callout-transparent code, .bs-callout-amber.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-amber .callout-arrow-left:before {
    border-left-color: #FFC107; }
  .bs-callout-amber .callout-arrow-right:before {
    border-right-color: #FFC107; }

.climacon-amber.climacon-darken-1 .climacon_component-stroke {
  fill: #FFB300; }

.climacon-amber.climacon-darken-2 .climacon_component-stroke {
  fill: #FFA000; }

.climacon-amber.climacon-darken-3 .climacon_component-stroke {
  fill: #FF8F00; }

.climacon-amber.climacon-darken-4 .climacon_component-stroke {
  fill: #FF6F00; }

.climacon-amber.climacon-accent-1 .climacon_component-stroke {
  fill: #FFE57F; }

.climacon-amber.climacon-accent-2 .climacon_component-stroke {
  fill: #FFD740; }

.climacon-amber.climacon-accent-3 .climacon_component-stroke {
  fill: #FFC400; }

.climacon-amber.climacon-accent-4 .climacon_component-stroke {
  fill: #FFAB00; }

.climacon-blue-grey.climacon-lighten-5 .climacon_component-stroke {
  fill: #ECEFF1; }

.climacon-blue-grey.climacon-lighten-4 .climacon_component-stroke {
  fill: #CFD8DC; }

.climacon-blue-grey.climacon-lighten-3 .climacon_component-stroke {
  fill: #B0BEC5; }

.climacon-blue-grey.climacon-lighten-2 .climacon_component-stroke {
  fill: #90A4AE; }

.climacon-blue-grey.climacon-lighten-1 .climacon_component-stroke {
  fill: #78909C; }

.bs-callout-blue-grey {
  display: block;
  border-color: #607D8B !important;
  background-color: #bdcbd1;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-blue-grey h1, .bs-callout-blue-grey h2, .bs-callout-blue-grey h3, .bs-callout-blue-grey h4, .bs-callout-blue-grey h5, .bs-callout-blue-grey h6 {
    margin-top: 0;
    color: #607D8B; }
  .bs-callout-blue-grey p:last-child {
    margin-bottom: 0; }
  .bs-callout-blue-grey code, .bs-callout-blue-grey .highlight {
    background-color: #fff; }
  .bs-callout-blue-grey.callout-transparent {
    display: block;
    border-color: #607D8B !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-blue-grey.callout-transparent h1, .bs-callout-blue-grey.callout-transparent h2, .bs-callout-blue-grey.callout-transparent h3, .bs-callout-blue-grey.callout-transparent h4, .bs-callout-blue-grey.callout-transparent h5, .bs-callout-blue-grey.callout-transparent h6 {
      margin-top: 0;
      color: #607D8B; }
    .bs-callout-blue-grey.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-blue-grey.callout-transparent code, .bs-callout-blue-grey.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-blue-grey .callout-arrow-left:before {
    border-left-color: #607D8B; }
  .bs-callout-blue-grey .callout-arrow-right:before {
    border-right-color: #607D8B; }

.climacon-blue-grey.climacon-darken-1 .climacon_component-stroke {
  fill: #546E7A; }

.climacon-blue-grey.climacon-darken-2 .climacon_component-stroke {
  fill: #455A64; }

.climacon-blue-grey.climacon-darken-3 .climacon_component-stroke {
  fill: #37474F; }

.climacon-blue-grey.climacon-darken-4 .climacon_component-stroke {
  fill: #263238; }

.climacon-grey-blue.climacon-lighten-5 .climacon_component-stroke {
  fill: #ECEFF1; }

.climacon-grey-blue.climacon-lighten-4 .climacon_component-stroke {
  fill: #CFD8DC; }

.climacon-grey-blue.climacon-lighten-3 .climacon_component-stroke {
  fill: #B0BEC5; }

.climacon-grey-blue.climacon-lighten-2 .climacon_component-stroke {
  fill: #6F85AD; }

.climacon-grey-blue.climacon-lighten-1 .climacon_component-stroke {
  fill: #78909C; }

.bs-callout-grey-blue {
  display: block;
  border-color: #1B2942 !important;
  background-color: #4b71b5;
  border-radius: 0.25rem;
  color: black; }
  .bs-callout-grey-blue h1, .bs-callout-grey-blue h2, .bs-callout-grey-blue h3, .bs-callout-grey-blue h4, .bs-callout-grey-blue h5, .bs-callout-grey-blue h6 {
    margin-top: 0;
    color: #1B2942; }
  .bs-callout-grey-blue p:last-child {
    margin-bottom: 0; }
  .bs-callout-grey-blue code, .bs-callout-grey-blue .highlight {
    background-color: #fff; }
  .bs-callout-grey-blue.callout-transparent {
    display: block;
    border-color: #1B2942 !important;
    background-color: transparent;
    border-radius: 0.25rem;
    color: black; }
    .bs-callout-grey-blue.callout-transparent h1, .bs-callout-grey-blue.callout-transparent h2, .bs-callout-grey-blue.callout-transparent h3, .bs-callout-grey-blue.callout-transparent h4, .bs-callout-grey-blue.callout-transparent h5, .bs-callout-grey-blue.callout-transparent h6 {
      margin-top: 0;
      color: #1B2942; }
    .bs-callout-grey-blue.callout-transparent p:last-child {
      margin-bottom: 0; }
    .bs-callout-grey-blue.callout-transparent code, .bs-callout-grey-blue.callout-transparent .highlight {
      background-color: #fff; }
  .bs-callout-grey-blue .callout-arrow-left:before {
    border-left-color: #1B2942; }
  .bs-callout-grey-blue .callout-arrow-right:before {
    border-right-color: #1B2942; }

.climacon-grey-blue.climacon-darken-1 .climacon_component-stroke {
  fill: #546E7A; }

.climacon-grey-blue.climacon-darken-2 .climacon_component-stroke {
  fill: #404E67; }

.climacon-grey-blue.climacon-darken-3 .climacon_component-stroke {
  fill: #37474F; }

.climacon-grey-blue.climacon-darken-4 .climacon_component-stroke {
  fill: #263238; }

.climacon-shades.climacon-black .climacon_component-stroke {
  fill: #000000; }

.climacon-shades.climacon-white .climacon_component-stroke {
  fill: #FFFFFF; }

.climacon-shades.climacon-transparent .climacon_component-stroke {
  fill: transparent; }

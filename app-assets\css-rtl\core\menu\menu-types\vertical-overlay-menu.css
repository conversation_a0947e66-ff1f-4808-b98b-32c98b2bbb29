/*=========================================================================================
	File Name: vertical-overlay-menu.scss
	Description: A overlay style vertical menu with show and hide support. It support 
	light & dark version, filpped layout, right side icons, native scroll and borders menu 
	item seperation.
	----------------------------------------------------------------------------------------
	Item Name: Stack - Responsive Admin Theme
	Version: 3.0
	Author: PIXINVENT
	Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
.vertical-overlay-menu .content
{
    margin-right: 0;
}

.vertical-overlay-menu .navbar .navbar-header
{
    float: right;

    width: 240px;
}

.vertical-overlay-menu .navbar.navbar-brand-center .navbar-container
{
    margin-right: 0;
}

.vertical-overlay-menu .navbar.navbar-brand-center .navbar-header
{
    float: right;

    width: auto;
}

.vertical-overlay-menu .main-menu,
.vertical-overlay-menu.menu-hide .main-menu
{
    right: -240px; 

    width: 240px;

    -webkit-transition: width .25s,opacity .25s,-webkit-transform .25s;
       -moz-transition: width .25s,opacity .25s,transform .25s,-moz-transform .25s;
         -o-transition: width .25s,opacity .25s,-o-transform .25s;
            transition: width .25s,opacity .25s,-webkit-transform .25s;
            transition: width .25s,opacity .25s,transform .25s;
            transition: width .25s,opacity .25s,transform .25s,-webkit-transform .25s,-moz-transform .25s,-o-transform .25s;
    -webkit-transform: translate3d(0, 0, 0);
       -moz-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);

    opacity: 0;
}
.vertical-overlay-menu .main-menu .navigation .navigation-header .ft-minus
{
    display: none;
}
.vertical-overlay-menu .main-menu .navigation > li > a > i
{
    font-size: 1.2rem;

    float: right; 

    margin-left: 12px;
}
.vertical-overlay-menu .main-menu .navigation > li > a > i:before
{
    -webkit-transition: 200ms ease all;
       -moz-transition: 200ms ease all;
         -o-transition: 200ms ease all;
            transition: 200ms ease all;
}
.vertical-overlay-menu .main-menu .navigation li.has-sub > a:not(.mm-next):after
{
    font-family: 'FontAwesome';
    font-size: 1rem;

    position: absolute;
    top: 10px;
    left: 20px;

    display: inline-block;

    content: '\f105';
    transition: -webkit-transform .2s ease-in-out; 
    -webkit-transform: rotate(0deg);
       -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
         -o-transform: rotate(0deg);
            transform: rotate(0deg);
}
.vertical-overlay-menu .main-menu .navigation li.open > a:not(.mm-next):after
{
    -webkit-transform: rotate(-90deg);
       -moz-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
         -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
}
.vertical-overlay-menu .main-menu .main-menu-footer
{
    bottom: 55px;
}
.vertical-overlay-menu .main-menu .main-menu-footer
{
    width: 240px;
}

.vertical-overlay-menu.menu-open .main-menu
{
    -webkit-transition: width .25s,opacity .25s,-webkit-transform .25s;
       -moz-transition: width .25s,opacity .25s,transform .25s,-moz-transform .25s;
         -o-transition: width .25s,opacity .25s,-o-transform .25s;
            transition: width .25s,opacity .25s,-webkit-transform .25s;
            transition: width .25s,opacity .25s,transform .25s;
            transition: width .25s,opacity .25s,transform .25s,-webkit-transform .25s,-moz-transform .25s,-o-transform .25s; 
    -webkit-transform: translate3d(-240px, 0, 0);
       -moz-transform: translate3d(-240px, 0, 0);
            transform: translate3d(-240px, 0, 0);

    opacity: 1;
}

.vertical-overlay-menu.menu-flipped .main-menu
{
    right: inherit; 
    left: -240px;
}

.vertical-overlay-menu.menu-flipped .navbar .navbar-container
{
    margin: 0;
    margin-left: 240px;
}

.vertical-overlay-menu.menu-flipped .navbar .navbar-header
{
    float: left;
}

.vertical-overlay-menu.menu-flipped.menu-open .main-menu
{
    -webkit-transform: translate3d(240px, 0, 0);
       -moz-transform: translate3d(240px, 0, 0);
            transform: translate3d(240px, 0, 0);
}

@media (max-width: 991.98px)
{
    .vertical-overlay-menu .main-menu .main-menu-footer
    {
        bottom: 0;
    }
}

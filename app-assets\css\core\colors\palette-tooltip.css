.tooltip-bg-white .tooltip-inner {
  background-color: #FFFFFF; }
  .tooltip-bg-white .tooltip-inner:before {
    border-top-color: #FFFFFF !important; }

.tooltip-text-white .tooltip-inner {
  color: #FFFFFF; }

.tooltip-bg-black .tooltip-inner {
  background-color: #000000; }
  .tooltip-bg-black .tooltip-inner:before {
    border-top-color: #000000 !important; }

.tooltip-text-black .tooltip-inner {
  color: #000000; }

.tooltip-bg-primary .tooltip-inner {
  background-color: #00B5B8; }
  .tooltip-bg-primary .tooltip-inner:before {
    border-top-color: #00B5B8 !important; }

.tooltip-text-primary .tooltip-inner {
  color: #00B5B8; }

.tooltip-bg-success .tooltip-inner {
  background-color: #16D39A; }
  .tooltip-bg-success .tooltip-inner:before {
    border-top-color: #16D39A !important; }

.tooltip-text-success .tooltip-inner {
  color: #16D39A; }

.tooltip-bg-info .tooltip-inner {
  background-color: #2DCEE3; }
  .tooltip-bg-info .tooltip-inner:before {
    border-top-color: #2DCEE3 !important; }

.tooltip-text-info .tooltip-inner {
  color: #2DCEE3; }

.tooltip-bg-warning .tooltip-inner {
  background-color: #FFA87D; }
  .tooltip-bg-warning .tooltip-inner:before {
    border-top-color: #FFA87D !important; }

.tooltip-text-warning .tooltip-inner {
  color: #FFA87D; }

.tooltip-bg-danger .tooltip-inner {
  background-color: #FF7588; }
  .tooltip-bg-danger .tooltip-inner:before {
    border-top-color: #FF7588 !important; }

.tooltip-text-danger .tooltip-inner {
  color: #FF7588; }

.tooltip-bg-red .tooltip-inner {
  background-color: #F44336; }
  .tooltip-bg-red .tooltip-inner:before {
    border-top-color: #F44336 !important; }

.tooltip-text-red .tooltip-inner {
  color: #F44336; }

.tooltip-bg-pink .tooltip-inner {
  background-color: #E91E63; }
  .tooltip-bg-pink .tooltip-inner:before {
    border-top-color: #E91E63 !important; }

.tooltip-text-pink .tooltip-inner {
  color: #E91E63; }

.tooltip-bg-purple .tooltip-inner {
  background-color: #9C27B0; }
  .tooltip-bg-purple .tooltip-inner:before {
    border-top-color: #9C27B0 !important; }

.tooltip-text-purple .tooltip-inner {
  color: #9C27B0; }

.tooltip-bg-blue .tooltip-inner {
  background-color: #2196F3; }
  .tooltip-bg-blue .tooltip-inner:before {
    border-top-color: #2196F3 !important; }

.tooltip-text-blue .tooltip-inner {
  color: #2196F3; }

.tooltip-bg-cyan .tooltip-inner {
  background-color: #00BCD4; }
  .tooltip-bg-cyan .tooltip-inner:before {
    border-top-color: #00BCD4 !important; }

.tooltip-text-cyan .tooltip-inner {
  color: #00BCD4; }

.tooltip-bg-teal .tooltip-inner {
  background-color: #009688; }
  .tooltip-bg-teal .tooltip-inner:before {
    border-top-color: #009688 !important; }

.tooltip-text-teal .tooltip-inner {
  color: #009688; }

.tooltip-bg-yellow .tooltip-inner {
  background-color: #FFEB3B; }
  .tooltip-bg-yellow .tooltip-inner:before {
    border-top-color: #FFEB3B !important; }

.tooltip-text-yellow .tooltip-inner {
  color: #FFEB3B; }

.tooltip-bg-amber .tooltip-inner {
  background-color: #FFC107; }
  .tooltip-bg-amber .tooltip-inner:before {
    border-top-color: #FFC107 !important; }

.tooltip-text-amber .tooltip-inner {
  color: #FFC107; }

.tooltip-bg-blue-grey .tooltip-inner {
  background-color: #607D8B; }
  .tooltip-bg-blue-grey .tooltip-inner:before {
    border-top-color: #607D8B !important; }

.tooltip-text-blue-grey .tooltip-inner {
  color: #607D8B; }

.tooltip-bg-grey-blue .tooltip-inner {
  background-color: #1B2942; }
  .tooltip-bg-grey-blue .tooltip-inner:before {
    border-top-color: #1B2942 !important; }

.tooltip-text-grey-blue .tooltip-inner {
  color: #1B2942; }

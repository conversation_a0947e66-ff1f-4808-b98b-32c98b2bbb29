@-webkit-keyframes ball-scale-ripple-multiple {
  0% {
    -webkit-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@-moz-keyframes ball-scale-ripple-multiple {
  0% {
    -moz-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -moz-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@-o-keyframes ball-scale-ripple-multiple {
  0% {
    -o-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

@keyframes ball-scale-ripple-multiple {
  0% {
    -webkit-transform: scale(0.1);
    -moz-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1; }
  70% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0.7; }
  100% {
    opacity: 0.0; } }

.ball-scale-ripple-multiple {
  position: relative;
  -webkit-transform: translateY(-25px);
  -moz-transform: translateY(-25px);
  -ms-transform: translateY(-25px);
  -o-transform: translateY(-25px);
  transform: translateY(-25px); }
  .ball-scale-ripple-multiple > div:nth-child(0) {
    -webkit-animation-delay: -0.8s;
    -moz-animation-delay: -0.8s;
    -o-animation-delay: -0.8s;
    animation-delay: -0.8s; }
  .ball-scale-ripple-multiple > div:nth-child(1) {
    -webkit-animation-delay: -0.6s;
    -moz-animation-delay: -0.6s;
    -o-animation-delay: -0.6s;
    animation-delay: -0.6s; }
  .ball-scale-ripple-multiple > div:nth-child(2) {
    -webkit-animation-delay: -0.4s;
    -moz-animation-delay: -0.4s;
    -o-animation-delay: -0.4s;
    animation-delay: -0.4s; }
  .ball-scale-ripple-multiple > div:nth-child(3) {
    -webkit-animation-delay: -0.2s;
    -moz-animation-delay: -0.2s;
    -o-animation-delay: -0.2s;
    animation-delay: -0.2s; }
  .ball-scale-ripple-multiple > div {
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    top: -2px;
    left: -26px;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    border: 2px solid #404E67;
    -webkit-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
    -moz-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
    -o-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
    animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8); }

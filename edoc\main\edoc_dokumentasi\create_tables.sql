-- <PERSON><PERSON> untuk membuat tabel dokumentasi kegiatan

-- Tabel edoc_dokumentasi
CREATE TABLE IF NOT EXISTS `edoc_dokumentasi` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `JUDUL` varchar(255) NOT NULL,
  `ID_KATEGORI` int(11) NOT NULL,
  `TANGGAL` date NOT NULL,
  `LOKASI` varchar(255) NOT NULL,
  `UNIT` varchar(10) NOT NULL,
  `ID_JENIS_DOK` int(11) NOT NULL,
  `KETERANGAN` text,
  `FILE_PATH` varchar(255) NOT NULL,
  `CREATED_BY` varchar(10) NOT NULL,
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `STATUS` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`ID`),
  KEY `idx_kategori` (`ID_KATEGORI`),
  KEY `idx_jenis_dok` (`ID_JENIS_DOK`),
  KEY `idx_unit` (`UNIT`),
  <PERSON><PERSON>Y `idx_status` (`STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Tabel edoc_dokumentasi_detail
CREATE TABLE IF NOT EXISTS `edoc_dokumentasi_detail` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ID_DOKUMENTASI` int(11) NOT NULL,
  `FILE_PATH` varchar(255) NOT NULL,
  `TIPE_FILE` enum('foto','video') NOT NULL,
  `LOKASI_FILE` varchar(255) NOT NULL,
  `UPLOADED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `STATUS` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`ID`),
  KEY `idx_dokumentasi` (`ID_DOKUMENTASI`),
  KEY `idx_tipe_file` (`TIPE_FILE`),
  KEY `idx_status` (`STATUS`),
  FOREIGN KEY (`ID_DOKUMENTASI`) REFERENCES `edoc_dokumentasi` (`ID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Tabel edoc_refrensi (jika belum ada)
CREATE TABLE IF NOT EXISTS `edoc_refrensi` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `JENIS` tinyint(2) NOT NULL COMMENT '1: Kategori Dokumentasi, 2: Jenis Dokumentasi',
  `DESKRIPSI` varchar(255) NOT NULL,
  `STATUS` tinyint(1) NOT NULL DEFAULT '1',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `idx_jenis` (`JENIS`),
  KEY `idx_status` (`STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert data sample untuk edoc_refrensi
INSERT IGNORE INTO `edoc_refrensi` (`JENIS`, `DESKRIPSI`, `STATUS`) VALUES
(1, 'Kegiatan Pelatihan', 1),
(1, 'Kegiatan Seminar', 1),
(1, 'Kegiatan Workshop', 1),
(1, 'Kegiatan Rapat', 1),
(1, 'Kegiatan Sosialisasi', 1),
(2, 'Dokumentasi Foto', 1),
(2, 'Dokumentasi Video', 1),
(2, 'Dokumentasi Campuran', 1);

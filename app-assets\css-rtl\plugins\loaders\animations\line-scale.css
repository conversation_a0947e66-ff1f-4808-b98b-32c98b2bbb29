@-webkit-keyframes line-scale
{
    0%
    {
        -webkit-transform: scaley(1);
                transform: scaley(1);
    }
    50%
    {
        -webkit-transform: scaley(.4);
                transform: scaley(.4);
    }
    100%
    {
        -webkit-transform: scaley(1);
                transform: scaley(1);
    }
}

@-moz-keyframes line-scale
{
    0%
    {
        -moz-transform: scaley(1);
             transform: scaley(1);
    }
    50%
    {
        -moz-transform: scaley(.4);
             transform: scaley(.4);
    }
    100%
    {
        -moz-transform: scaley(1);
             transform: scaley(1);
    }
}

@-o-keyframes line-scale
{
    0%
    {
        -o-transform: scaley(1);
           transform: scaley(1);
    }
    50%
    {
        -o-transform: scaley(.4);
           transform: scaley(.4);
    }
    100%
    {
        -o-transform: scaley(1);
           transform: scaley(1);
    }
}

@keyframes line-scale
{
    0%
    {
        -webkit-transform: scaley(1);
           -moz-transform: scaley(1);
             -o-transform: scaley(1);
                transform: scaley(1);
    }
    50%
    {
        -webkit-transform: scaley(.4);
           -moz-transform: scaley(.4);
             -o-transform: scaley(.4);
                transform: scaley(.4);
    }
    100%
    {
        -webkit-transform: scaley(1);
           -moz-transform: scaley(1);
             -o-transform: scaley(1);
                transform: scaley(1);
    }
}

.line-scale > div:nth-child(1)
{
    -webkit-animation: line-scale 1s -.4s infinite cubic-bezier(.2, .68, .18, 1.08);
       -moz-animation: line-scale 1s -.4s infinite cubic-bezier(.2, .68, .18, 1.08);
         -o-animation: line-scale 1s -.4s infinite cubic-bezier(.2, .68, .18, 1.08);
            animation: line-scale 1s -.4s infinite cubic-bezier(.2, .68, .18, 1.08);
}

.line-scale > div:nth-child(2)
{
    -webkit-animation: line-scale 1s -.3s infinite cubic-bezier(.2, .68, .18, 1.08);
       -moz-animation: line-scale 1s -.3s infinite cubic-bezier(.2, .68, .18, 1.08);
         -o-animation: line-scale 1s -.3s infinite cubic-bezier(.2, .68, .18, 1.08);
            animation: line-scale 1s -.3s infinite cubic-bezier(.2, .68, .18, 1.08);
}

.line-scale > div:nth-child(3)
{
    -webkit-animation: line-scale 1s -.2s infinite cubic-bezier(.2, .68, .18, 1.08);
       -moz-animation: line-scale 1s -.2s infinite cubic-bezier(.2, .68, .18, 1.08);
         -o-animation: line-scale 1s -.2s infinite cubic-bezier(.2, .68, .18, 1.08);
            animation: line-scale 1s -.2s infinite cubic-bezier(.2, .68, .18, 1.08);
}

.line-scale > div:nth-child(4)
{
    -webkit-animation: line-scale 1s -.1s infinite cubic-bezier(.2, .68, .18, 1.08);
       -moz-animation: line-scale 1s -.1s infinite cubic-bezier(.2, .68, .18, 1.08);
         -o-animation: line-scale 1s -.1s infinite cubic-bezier(.2, .68, .18, 1.08);
            animation: line-scale 1s -.1s infinite cubic-bezier(.2, .68, .18, 1.08);
}

.line-scale > div:nth-child(5)
{
    -webkit-animation: line-scale 1s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
       -moz-animation: line-scale 1s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
         -o-animation: line-scale 1s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
            animation: line-scale 1s 0s infinite cubic-bezier(.2, .68, .18, 1.08);
}

.line-scale > div
{
    display: inline-block; 

    width: 4px;
    height: 3.45rem;
    margin: 2px;

    border-radius: 2px;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

.ball-rotate,.ball-rotate>div{position:relative}.ball-rotate>div,.ball-rotate>div:after,.ball-rotate>div:before{width:15px;height:15px;margin:2px;border-radius:100%;background-color:#404e67}@-webkit-keyframes rotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-webkit-transform:rotate(-360deg);transform:rotate(-360deg)}}@-moz-keyframes rotate{0%{-moz-transform:rotate(0);transform:rotate(0)}50%{-moz-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-moz-transform:rotate(-360deg);transform:rotate(-360deg)}}@-o-keyframes rotate{0%{-o-transform:rotate(0);transform:rotate(0)}50%{-o-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-o-transform:rotate(-360deg);transform:rotate(-360deg)}}@keyframes rotate{0%{-webkit-transform:rotate(0);-moz-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0)}50%{-webkit-transform:rotate(-180deg);-moz-transform:rotate(-180deg);-o-transform:rotate(-180deg);transform:rotate(-180deg)}100%{-webkit-transform:rotate(-360deg);-moz-transform:rotate(-360deg);-o-transform:rotate(-360deg);transform:rotate(-360deg)}}.ball-rotate>div{-webkit-animation-fill-mode:both;-moz-animation-fill-mode:both;-o-animation-fill-mode:both;animation-fill-mode:both}.ball-rotate>div:first-child{-webkit-animation:rotate 1s 0s cubic-bezier(.7,-.13,.22,.86) infinite;-moz-animation:rotate 1s 0s cubic-bezier(.7,-.13,.22,.86) infinite;-o-animation:rotate 1s 0s cubic-bezier(.7,-.13,.22,.86) infinite;animation:rotate 1s 0s cubic-bezier(.7,-.13,.22,.86) infinite}.ball-rotate>div:after,.ball-rotate>div:before{position:absolute;content:'';opacity:.8}.ball-rotate>div:before{top:0;right:-28px}.ball-rotate>div:after{top:0;right:25px}
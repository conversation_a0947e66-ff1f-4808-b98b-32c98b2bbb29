<?php
include '../../config.php';

echo "<h2>Insert Demo Data - Dokumentasi Kegiatan</h2>";

// Demo data untuk dokumentasi
$demoData = [
    [
        'judul' => 'Pelatihan Keselamatan Kerja',
        'id_kategori' => 1, // Kegiatan Pelatihan
        'tanggal' => '2024-01-15',
        'lokasi' => 'Aula Utama RSK Dharmais',
        'unit' => '5001', // Sesuaikan dengan ID unit yang ada
        'id_jenis_dok' => 8, // Dokumentasi Campuran
        'keterangan' => 'Pelatihan keselamatan kerja untuk seluruh karyawan RSK Dharmais tahun 2024',
        'FILE_PATH' => '/file_dokumentasi/Pelatihan_Keselamatan_Kerja_20240115',
        'created_by' => '90901'
    ],
    [
        'judul' => 'Seminar Kesehatan Mental',
        'id_kategori' => 2, // Kegiatan Seminar
        'tanggal' => '2024-01-20',
        'lokasi' => '<PERSON>uang Pertemuan Lt. 2',
        'unit' => '5002',
        'id_jenis_dok' => 6, // Dokumentasi Foto
        'keterangan' => 'Seminar tentang pentingnya kesehatan mental di lingkungan kerja',
        'FILE_PATH' => '/file_dokumentasi/Seminar_Kesehatan_Mental_20240120',
        'created_by' => '90901'
    ],
    [
        'judul' => 'Workshop Pelayanan Prima',
        'id_kategori' => 3, // Kegiatan Workshop
        'tanggal' => '2024-01-25',
        'lokasi' => 'Ruang Training',
        'unit' => '5003',
        'id_jenis_dok' => 7, // Dokumentasi Video
        'keterangan' => 'Workshop untuk meningkatkan kualitas pelayanan kepada pasien',
        'FILE_PATH' => '/file_dokumentasi/Workshop_Pelayanan_Prima_20240125',
        'created_by' => '90901'
    ]
];

echo "<h3>Menambahkan Demo Data...</h3>";

foreach ($demoData as $index => $data) {
    $sql = "INSERT INTO edoc_dokumentasi 
            (JUDUL, ID_KATEGORI, TANGGAL, LOKASI, UNIT, ID_JENIS_DOK, KETERANGAN, FILE_PATH, CREATED_BY, CREATED_AT, STATUS) 
            VALUES 
            ('{$data['judul']}', {$data['id_kategori']}, '{$data['tanggal']}', '{$data['lokasi']}', '{$data['unit']}', {$data['id_jenis_dok']}, '{$data['keterangan']}', '{$data['FILE_PATH']}', '{$data['created_by']}', NOW(), 1)";
    
    if (mysqli_query($koneksi, $sql)) {
        $dokumentasiId = mysqli_insert_id($koneksi);
        echo "<p style='color: green;'>✓ Demo data " . ($index + 1) . ": {$data['judul']} (ID: $dokumentasiId)</p>";
        
        // Tambahkan demo file detail
        $demoFiles = [];
        
        if ($data['id_jenis_dok'] == 6 || $data['id_jenis_dok'] == 8) { // Foto atau Campuran
            $demoFiles[] = [
                'file_path' => 'foto_kegiatan_1.jpg',
                'tipe_file' => 'foto',
                'lokasi_file' => $data['FILE_PATH']
            ];
            $demoFiles[] = [
                'file_path' => 'foto_kegiatan_2.jpg',
                'tipe_file' => 'foto',
                'lokasi_file' => $data['FILE_PATH']
            ];
        }
        
        if ($data['id_jenis_dok'] == 7 || $data['id_jenis_dok'] == 8) { // Video atau Campuran
            $demoFiles[] = [
                'file_path' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'tipe_file' => 'video',
                'lokasi_file' => $data['FILE_PATH']
            ];
        }
        
        // Insert demo files
        foreach ($demoFiles as $file) {
            $sqlFile = "INSERT INTO edoc_dokumentasi_detail 
                        (ID_DOKUMENTASI, FILE_PATH, TIPE_FILE, LOKASI_FILE, UPLOADED_AT, STATUS) 
                        VALUES 
                        ($dokumentasiId, '{$file['file_path']}', '{$file['tipe_file']}', '{$file['lokasi_file']}', NOW(), 1)";
            
            if (mysqli_query($koneksi, $sqlFile)) {
                echo "<p style='margin-left: 20px; color: blue;'>→ File: {$file['file_path']} ({$file['tipe_file']})</p>";
            } else {
                echo "<p style='margin-left: 20px; color: red;'>✗ Error file: " . mysqli_error($koneksi) . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ Error demo data " . ($index + 1) . ": " . mysqli_error($koneksi) . "</p>";
    }
}

// Cek hasil
echo "<h3>Verifikasi Data:</h3>";
$result = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM edoc_dokumentasi WHERE STATUS = 1");
$total = mysqli_fetch_array($result)['total'];
echo "<p style='color: green;'>Total dokumentasi: $total</p>";

$resultDetail = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM edoc_dokumentasi_detail WHERE STATUS = 1");
$totalDetail = mysqli_fetch_array($resultDetail)['total'];
echo "<p style='color: green;'>Total file detail: $totalDetail</p>";

// Tampilkan data yang baru ditambahkan
echo "<h3>Data Terbaru:</h3>";
$recent = mysqli_query($koneksi, "
    SELECT ed.JUDUL, ed.TANGGAL, er1.DESKRIPSI as KATEGORI, er2.DESKRIPSI as JENIS_DOK
    FROM edoc_dokumentasi ed
    LEFT JOIN edoc_refrensi er1 ON ed.ID_KATEGORI = er1.ID
    LEFT JOIN edoc_refrensi er2 ON ed.ID_JENIS_DOK = er2.ID
    WHERE ed.STATUS = 1
    ORDER BY ed.CREATED_AT DESC
    LIMIT 10
");

if (mysqli_num_rows($recent) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Judul</th><th>Tanggal</th><th>Kategori</th><th>Jenis</th></tr>";
    
    while ($row = mysqli_fetch_array($recent)) {
        echo "<tr>";
        echo "<td>{$row['JUDUL']}</td>";
        echo "<td>{$row['TANGGAL']}</td>";
        echo "<td>{$row['KATEGORI']}</td>";
        echo "<td>{$row['JENIS_DOK']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Tidak ada data</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Kembali ke Dokumentasi Kegiatan</a></p>";
echo "<p><a href='test_connection.php'>→ Test Connection</a></p>";
?>

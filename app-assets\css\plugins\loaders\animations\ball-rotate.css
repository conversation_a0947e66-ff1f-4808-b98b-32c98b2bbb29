@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
  50% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-moz-keyframes rotate {
  0% {
    -moz-transform: rotate(0deg);
    transform: rotate(0deg); }
  50% {
    -moz-transform: rotate(180deg);
    transform: rotate(180deg); }
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-o-keyframes rotate {
  0% {
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  50% {
    -o-transform: rotate(180deg);
    transform: rotate(180deg); }
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg); }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

.ball-rotate {
  position: relative; }
  .ball-rotate > div {
    background-color: #404E67;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    margin: 2px;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: relative; }
    .ball-rotate > div:first-child {
      -webkit-animation: rotate 1s 0s cubic-bezier(0.7, -0.13, 0.22, 0.86) infinite;
      -moz-animation: rotate 1s 0s cubic-bezier(0.7, -0.13, 0.22, 0.86) infinite;
      -o-animation: rotate 1s 0s cubic-bezier(0.7, -0.13, 0.22, 0.86) infinite;
      animation: rotate 1s 0s cubic-bezier(0.7, -0.13, 0.22, 0.86) infinite; }
    .ball-rotate > div:before, .ball-rotate > div:after {
      background-color: #404E67;
      width: 15px;
      height: 15px;
      border-radius: 100%;
      margin: 2px;
      content: "";
      position: absolute;
      opacity: 0.8; }
    .ball-rotate > div:before {
      top: 0px;
      left: -28px; }
    .ball-rotate > div:after {
      top: 0px;
      left: 25px; }

<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Fungsi untuk membuat nama folder yang aman
function createSafeFileName($string) {
    // Hapus karakter khusus dan ganti dengan underscore
    $string = preg_replace('/[^A-Za-z0-9\-_]/', '_', $string);
    // Hapus underscore berturut-turut
    $string = preg_replace('/_+/', '_', $string);
    // Hapus underscore di awal dan akhir
    $string = trim($string, '_');
    return $string;
}

try {
    // Validasi semua field required terlebih dahulu
    $required_fields = [
        'judul' => 'Judul',
        'id_kategori' => 'Kategori',
        'tanggal' => 'Tanggal',
        'lokasi' => 'Lokasi',
        'unit' => 'Unit',
        'id_jenis_dok' => 'Jenis Dokumentasi'
    ];

    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            throw new Exception('Field "' . $label . '" wajib diisi');
        }
    }

    // Ambil dan bersihkan data dari form
    $judul = mysqli_real_escape_string($koneksi, $_POST['judul']);
    $id_kategori = intval($_POST['id_kategori']);
    $tanggal = mysqli_real_escape_string($koneksi, $_POST['tanggal']);
    $lokasi = mysqli_real_escape_string($koneksi, $_POST['lokasi']);
    $unit = mysqli_real_escape_string($koneksi, $_POST['unit']);
    $id_jenis_dok = intval($_POST['id_jenis_dok']);
    $keterangan = mysqli_real_escape_string($koneksi, isset($_POST['keterangan']) ? $_POST['keterangan'] : '');

    // Tentukan CREATED_BY berdasarkan hak akses
    $created_by = ($hak_akses == '1') ? '90901' : $user;

    // Ambil data file
    $fotoFiles = isset($_FILES['foto']) ? $_FILES['foto'] : null;
    $videoUrls = isset($_POST['video']) ? array_filter($_POST['video']) : [];
    
    // Set flag hasFile untuk digunakan nanti
    $hasFile = ($fotoFiles && count($fotoFiles['name']) > 0 && !empty($fotoFiles['name'][0])) || 
               (!empty($videoUrls));

    // Validasi file setelah semua field tervalidasi tapi sebelum operasi database
    if (!$hasFile) {
        throw new Exception('Minimal harus ada satu foto atau video yang diupload/diinput');
    }

    // Buat nama folder berdasarkan judul dan tanggal
    $folderName = createSafeFileName($judul) . '_' . str_replace('-', '', $tanggal);
    $uploadPath = '../../../edoc/main/file_dokumentasi/' . $folderName;
    $relativePath = '/file_dokumentasi/' . $folderName;

    // Buat folder jika belum ada
    if (!file_exists($uploadPath)) {
        if (!mkdir($uploadPath, 0755, true)) {
            throw new Exception('Gagal membuat folder upload');
        }
    }

    // Mulai transaksi
    mysqli_autocommit($koneksi, false);

    // Insert data utama dokumentasi
    $sqlInsert = "INSERT INTO edoc_dokumentasi 
                  (JUDUL, ID_KATEGORI, TANGGAL, LOKASI, UNIT, ID_JENIS_DOK, KETERANGAN, FOLDER_PATH, CREATED_BY, CREATED_AT, STATUS) 
                  VALUES 
                  ('$judul', $id_kategori, '$tanggal', '$lokasi', '$unit', $id_jenis_dok, '$keterangan', '$relativePath', '$created_by', NOW(), 1)";

    if (!mysqli_query($koneksi, $sqlInsert)) {
        throw new Exception('Gagal menyimpan data dokumentasi: ' . mysqli_error($koneksi));
    }

    $dokumentasiId = mysqli_insert_id($koneksi);

    $uploadedFiles = 0;

    // Proses upload foto
    if ($fotoFiles && count($fotoFiles['name']) > 0 && !empty($fotoFiles['name'][0])) {
        for ($i = 0; $i < count($fotoFiles['name']); $i++) {
            if ($fotoFiles['error'][$i] == UPLOAD_ERR_OK) {
                $fileName = $fotoFiles['name'][$i];
                $fileTmp = $fotoFiles['tmp_name'][$i];
                $fileSize = $fotoFiles['size'][$i];
                $fileType = $fotoFiles['type'][$i];

                // Validasi tipe file
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!in_array($fileType, $allowedTypes)) {
                    continue; // Skip file yang tidak valid
                }

                // Validasi ukuran file (max 5MB)
                if ($fileSize > 5 * 1024 * 1024) {
                    continue; // Skip file yang terlalu besar
                }

                // Buat nama file baru
                $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
                $baseName = pathinfo($fileName, PATHINFO_FILENAME);
                $newFileName = createSafeFileName($baseName) . '_' . ($i + 1) . '.' . $fileExtension;
                $filePath = $uploadPath . '/' . $newFileName;

                // Upload file
                if (move_uploaded_file($fileTmp, $filePath)) {
                    // Insert ke database
                    $sqlDetail = "INSERT INTO edoc_dokumentasi_detail 
                                  (ID_DOKUMENTASI, FILE_PATH, TIPE_FILE, LOKASI_FILE, UPLOADED_AT, STATUS) 
                                  VALUES 
                                  ($dokumentasiId, '$newFileName', 'foto', '$relativePath', NOW(), 1)";
                    
                    if (mysqli_query($koneksi, $sqlDetail)) {
                        $uploadedFiles++;
                    }
                }
            }
        }
    }

    // Proses video URLs
    if (!empty($videoUrls)) {
        foreach ($videoUrls as $index => $videoUrl) {
            if (!empty(trim($videoUrl))) {
                $cleanUrl = mysqli_real_escape_string($koneksi, trim($videoUrl));
                
                // Validasi URL (basic)
                if (filter_var($cleanUrl, FILTER_VALIDATE_URL)) {
                    $sqlDetail = "INSERT INTO edoc_dokumentasi_detail 
                                  (ID_DOKUMENTASI, FILE_PATH, TIPE_FILE, LOKASI_FILE, UPLOADED_AT, STATUS) 
                                  VALUES 
                                  ($dokumentasiId, '$cleanUrl', 'video', '$relativePath', NOW(), 1)";
                    
                    if (mysqli_query($koneksi, $sqlDetail)) {
                        $uploadedFiles++;
                    }
                }
            }
        }
    }

    // Cek apakah ada file yang berhasil diupload
    if ($uploadedFiles == 0) {
        throw new Exception('Tidak ada file yang berhasil disimpan');
    }

    // Commit transaksi
    mysqli_commit($koneksi);
    mysqli_autocommit($koneksi, true);

    echo json_encode([
        'status' => 'success', 
        'message' => 'Dokumentasi berhasil disimpan dengan ' . $uploadedFiles . ' file'
    ]);

} catch (Exception $e) {
    // Rollback transaksi jika ada error
    mysqli_rollback($koneksi);
    mysqli_autocommit($koneksi, true);
    
    echo json_encode([
        'status' => 'error', 
        'message' => $e->getMessage()
    ]);
}
?>

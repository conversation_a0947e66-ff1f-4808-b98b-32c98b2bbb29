@-webkit-keyframes ball-grid-beat {
  50% {
    opacity: 0.7; }
  100% {
    opacity: 1; } }

@-moz-keyframes ball-grid-beat {
  50% {
    opacity: 0.7; }
  100% {
    opacity: 1; } }

@-o-keyframes ball-grid-beat {
  50% {
    opacity: 0.7; }
  100% {
    opacity: 1; } }

@keyframes ball-grid-beat {
  50% {
    opacity: 0.7; }
  100% {
    opacity: 1; } }

.ball-grid-beat {
  width: 57px; }
  .ball-grid-beat > div:nth-child(1) {
    -webkit-animation-delay: -0.14s;
    -moz-animation-delay: -0.14s;
    -o-animation-delay: -0.14s;
    animation-delay: -0.14s;
    -webkit-animation-duration: 1.12s;
    -moz-animation-duration: 1.12s;
    -o-animation-duration: 1.12s;
    animation-duration: 1.12s; }
  .ball-grid-beat > div:nth-child(2) {
    -webkit-animation-delay: 0.51s;
    -moz-animation-delay: 0.51s;
    -o-animation-delay: 0.51s;
    animation-delay: 0.51s;
    -webkit-animation-duration: 0.92s;
    -moz-animation-duration: 0.92s;
    -o-animation-duration: 0.92s;
    animation-duration: 0.92s; }
  .ball-grid-beat > div:nth-child(3) {
    -webkit-animation-delay: 0.68s;
    -moz-animation-delay: 0.68s;
    -o-animation-delay: 0.68s;
    animation-delay: 0.68s;
    -webkit-animation-duration: 1.54s;
    -moz-animation-duration: 1.54s;
    -o-animation-duration: 1.54s;
    animation-duration: 1.54s; }
  .ball-grid-beat > div:nth-child(4) {
    -webkit-animation-delay: 0.47s;
    -moz-animation-delay: 0.47s;
    -o-animation-delay: 0.47s;
    animation-delay: 0.47s;
    -webkit-animation-duration: 0.98s;
    -moz-animation-duration: 0.98s;
    -o-animation-duration: 0.98s;
    animation-duration: 0.98s; }
  .ball-grid-beat > div:nth-child(5) {
    -webkit-animation-delay: 0.76s;
    -moz-animation-delay: 0.76s;
    -o-animation-delay: 0.76s;
    animation-delay: 0.76s;
    -webkit-animation-duration: 1.57s;
    -moz-animation-duration: 1.57s;
    -o-animation-duration: 1.57s;
    animation-duration: 1.57s; }
  .ball-grid-beat > div:nth-child(6) {
    -webkit-animation-delay: 0.24s;
    -moz-animation-delay: 0.24s;
    -o-animation-delay: 0.24s;
    animation-delay: 0.24s;
    -webkit-animation-duration: 1s;
    -moz-animation-duration: 1s;
    -o-animation-duration: 1s;
    animation-duration: 1s; }
  .ball-grid-beat > div:nth-child(7) {
    -webkit-animation-delay: 0.59s;
    -moz-animation-delay: 0.59s;
    -o-animation-delay: 0.59s;
    animation-delay: 0.59s;
    -webkit-animation-duration: 1.47s;
    -moz-animation-duration: 1.47s;
    -o-animation-duration: 1.47s;
    animation-duration: 1.47s; }
  .ball-grid-beat > div:nth-child(8) {
    -webkit-animation-delay: 0.18s;
    -moz-animation-delay: 0.18s;
    -o-animation-delay: 0.18s;
    animation-delay: 0.18s;
    -webkit-animation-duration: 0.88s;
    -moz-animation-duration: 0.88s;
    -o-animation-duration: 0.88s;
    animation-duration: 0.88s; }
  .ball-grid-beat > div:nth-child(9) {
    -webkit-animation-delay: 0.75s;
    -moz-animation-delay: 0.75s;
    -o-animation-delay: 0.75s;
    animation-delay: 0.75s;
    -webkit-animation-duration: 1.02s;
    -moz-animation-duration: 1.02s;
    -o-animation-duration: 1.02s;
    animation-duration: 1.02s; }
  .ball-grid-beat > div {
    background-color: #404E67;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    margin: 2px;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    display: inline-block;
    float: left;
    -webkit-animation-name: ball-grid-beat;
    -moz-animation-name: ball-grid-beat;
    -o-animation-name: ball-grid-beat;
    animation-name: ball-grid-beat;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    -o-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-delay: 0;
    -moz-animation-delay: 0;
    -o-animation-delay: 0;
    animation-delay: 0; }

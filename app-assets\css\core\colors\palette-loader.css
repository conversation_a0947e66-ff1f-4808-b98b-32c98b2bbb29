.loader-white div {
  background-color: #FFFFFF !important; }

.loader-white.ball-clip-rotate div {
  border-color: #FFFFFF !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-white.ball-clip-rotate-pulse div:first-child {
  background: #FFFFFF !important; }

.loader-white.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #FFFFFF transparent #FFFFFF transparent !important; }

.loader-white.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #FFFFFF transparent #FFFFFF !important; }

.loader-white.ball-rotate div:before, .loader-white.ball-rotate div:after {
  background-color: #FFFFFF !important; }

.loader-white.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #FFFFFF !important; }

.loader-white.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #FFFFFF !important; }

.loader-white.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #FFFFFF !important; }

.loader-white.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #FFFFFF !important; }

.loader-white.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #FFFFFF), to(#FFFFFF));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #FFFFFF 30%, #FFFFFF 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #FFFFFF 30%, #FFFFFF 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #FFFFFF 30%, #FFFFFF 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #FFFFFF 30%, #FFFFFF 100%);
  background-color: transparent !important; }

.loader-white.fading-circle div {
  background-color: transparent !important; }
  .loader-white.fading-circle div:before {
    background-color: #FFFFFF !important; }

.loader-white.folding-cube div {
  background-color: transparent !important; }
  .loader-white.folding-cube div:before {
    background-color: #FFFFFF !important; }

.loader-black div {
  background-color: #000000 !important; }

.loader-black.ball-clip-rotate div {
  border-color: #000000 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-black.ball-clip-rotate-pulse div:first-child {
  background: #000000 !important; }

.loader-black.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #000000 transparent #000000 transparent !important; }

.loader-black.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #000000 transparent #000000 !important; }

.loader-black.ball-rotate div:before, .loader-black.ball-rotate div:after {
  background-color: #000000 !important; }

.loader-black.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #000000 !important; }

.loader-black.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #000000 !important; }

.loader-black.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #000000 !important; }

.loader-black.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #000000 !important; }

.loader-black.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #000000), to(#000000));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #000000 30%, #000000 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #000000 30%, #000000 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #000000 30%, #000000 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #000000 30%, #000000 100%);
  background-color: transparent !important; }

.loader-black.fading-circle div {
  background-color: transparent !important; }
  .loader-black.fading-circle div:before {
    background-color: #000000 !important; }

.loader-black.folding-cube div {
  background-color: transparent !important; }
  .loader-black.folding-cube div:before {
    background-color: #000000 !important; }

.loader-primary div {
  background-color: #00B5B8 !important; }

.loader-primary.ball-clip-rotate div {
  border-color: #00B5B8 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-primary.ball-clip-rotate-pulse div:first-child {
  background: #00B5B8 !important; }

.loader-primary.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #00B5B8 transparent #00B5B8 transparent !important; }

.loader-primary.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #00B5B8 transparent #00B5B8 !important; }

.loader-primary.ball-rotate div:before, .loader-primary.ball-rotate div:after {
  background-color: #00B5B8 !important; }

.loader-primary.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #00B5B8 !important; }

.loader-primary.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #00B5B8 !important; }

.loader-primary.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #00B5B8 !important; }

.loader-primary.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #00B5B8 !important; }

.loader-primary.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #00B5B8), to(#00B5B8));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #00B5B8 30%, #00B5B8 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #00B5B8 30%, #00B5B8 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #00B5B8 30%, #00B5B8 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #00B5B8 30%, #00B5B8 100%);
  background-color: transparent !important; }

.loader-primary.fading-circle div {
  background-color: transparent !important; }
  .loader-primary.fading-circle div:before {
    background-color: #00B5B8 !important; }

.loader-primary.folding-cube div {
  background-color: transparent !important; }
  .loader-primary.folding-cube div:before {
    background-color: #00B5B8 !important; }

.loader-success div {
  background-color: #16D39A !important; }

.loader-success.ball-clip-rotate div {
  border-color: #16D39A !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-success.ball-clip-rotate-pulse div:first-child {
  background: #16D39A !important; }

.loader-success.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #16D39A transparent #16D39A transparent !important; }

.loader-success.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #16D39A transparent #16D39A !important; }

.loader-success.ball-rotate div:before, .loader-success.ball-rotate div:after {
  background-color: #16D39A !important; }

.loader-success.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #16D39A !important; }

.loader-success.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #16D39A !important; }

.loader-success.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #16D39A !important; }

.loader-success.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #16D39A !important; }

.loader-success.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #16D39A), to(#16D39A));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #16D39A 30%, #16D39A 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #16D39A 30%, #16D39A 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #16D39A 30%, #16D39A 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #16D39A 30%, #16D39A 100%);
  background-color: transparent !important; }

.loader-success.fading-circle div {
  background-color: transparent !important; }
  .loader-success.fading-circle div:before {
    background-color: #16D39A !important; }

.loader-success.folding-cube div {
  background-color: transparent !important; }
  .loader-success.folding-cube div:before {
    background-color: #16D39A !important; }

.loader-info div {
  background-color: #2DCEE3 !important; }

.loader-info.ball-clip-rotate div {
  border-color: #2DCEE3 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-info.ball-clip-rotate-pulse div:first-child {
  background: #2DCEE3 !important; }

.loader-info.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #2DCEE3 transparent #2DCEE3 transparent !important; }

.loader-info.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #2DCEE3 transparent #2DCEE3 !important; }

.loader-info.ball-rotate div:before, .loader-info.ball-rotate div:after {
  background-color: #2DCEE3 !important; }

.loader-info.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #2DCEE3 !important; }

.loader-info.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #2DCEE3 !important; }

.loader-info.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #2DCEE3 !important; }

.loader-info.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #2DCEE3 !important; }

.loader-info.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #2DCEE3), to(#2DCEE3));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #2DCEE3 30%, #2DCEE3 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #2DCEE3 30%, #2DCEE3 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #2DCEE3 30%, #2DCEE3 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #2DCEE3 30%, #2DCEE3 100%);
  background-color: transparent !important; }

.loader-info.fading-circle div {
  background-color: transparent !important; }
  .loader-info.fading-circle div:before {
    background-color: #2DCEE3 !important; }

.loader-info.folding-cube div {
  background-color: transparent !important; }
  .loader-info.folding-cube div:before {
    background-color: #2DCEE3 !important; }

.loader-warning div {
  background-color: #FFA87D !important; }

.loader-warning.ball-clip-rotate div {
  border-color: #FFA87D !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-warning.ball-clip-rotate-pulse div:first-child {
  background: #FFA87D !important; }

.loader-warning.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #FFA87D transparent #FFA87D transparent !important; }

.loader-warning.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #FFA87D transparent #FFA87D !important; }

.loader-warning.ball-rotate div:before, .loader-warning.ball-rotate div:after {
  background-color: #FFA87D !important; }

.loader-warning.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #FFA87D !important; }

.loader-warning.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #FFA87D !important; }

.loader-warning.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #FFA87D !important; }

.loader-warning.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #FFA87D !important; }

.loader-warning.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #FFA87D), to(#FFA87D));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #FFA87D 30%, #FFA87D 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #FFA87D 30%, #FFA87D 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #FFA87D 30%, #FFA87D 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #FFA87D 30%, #FFA87D 100%);
  background-color: transparent !important; }

.loader-warning.fading-circle div {
  background-color: transparent !important; }
  .loader-warning.fading-circle div:before {
    background-color: #FFA87D !important; }

.loader-warning.folding-cube div {
  background-color: transparent !important; }
  .loader-warning.folding-cube div:before {
    background-color: #FFA87D !important; }

.loader-danger div {
  background-color: #FF7588 !important; }

.loader-danger.ball-clip-rotate div {
  border-color: #FF7588 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-danger.ball-clip-rotate-pulse div:first-child {
  background: #FF7588 !important; }

.loader-danger.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #FF7588 transparent #FF7588 transparent !important; }

.loader-danger.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #FF7588 transparent #FF7588 !important; }

.loader-danger.ball-rotate div:before, .loader-danger.ball-rotate div:after {
  background-color: #FF7588 !important; }

.loader-danger.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #FF7588 !important; }

.loader-danger.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #FF7588 !important; }

.loader-danger.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #FF7588 !important; }

.loader-danger.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #FF7588 !important; }

.loader-danger.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #FF7588), to(#FF7588));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #FF7588 30%, #FF7588 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #FF7588 30%, #FF7588 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #FF7588 30%, #FF7588 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #FF7588 30%, #FF7588 100%);
  background-color: transparent !important; }

.loader-danger.fading-circle div {
  background-color: transparent !important; }
  .loader-danger.fading-circle div:before {
    background-color: #FF7588 !important; }

.loader-danger.folding-cube div {
  background-color: transparent !important; }
  .loader-danger.folding-cube div:before {
    background-color: #FF7588 !important; }

.loader-red div {
  background-color: #F44336 !important; }

.loader-red.ball-clip-rotate div {
  border-color: #F44336 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-red.ball-clip-rotate-pulse div:first-child {
  background: #F44336 !important; }

.loader-red.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #F44336 transparent #F44336 transparent !important; }

.loader-red.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #F44336 transparent #F44336 !important; }

.loader-red.ball-rotate div:before, .loader-red.ball-rotate div:after {
  background-color: #F44336 !important; }

.loader-red.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #F44336 !important; }

.loader-red.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #F44336 !important; }

.loader-red.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #F44336 !important; }

.loader-red.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #F44336 !important; }

.loader-red.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #F44336), to(#F44336));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #F44336 30%, #F44336 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #F44336 30%, #F44336 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #F44336 30%, #F44336 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #F44336 30%, #F44336 100%);
  background-color: transparent !important; }

.loader-red.fading-circle div {
  background-color: transparent !important; }
  .loader-red.fading-circle div:before {
    background-color: #F44336 !important; }

.loader-red.folding-cube div {
  background-color: transparent !important; }
  .loader-red.folding-cube div:before {
    background-color: #F44336 !important; }

.loader-pink div {
  background-color: #E91E63 !important; }

.loader-pink.ball-clip-rotate div {
  border-color: #E91E63 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-pink.ball-clip-rotate-pulse div:first-child {
  background: #E91E63 !important; }

.loader-pink.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #E91E63 transparent #E91E63 transparent !important; }

.loader-pink.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #E91E63 transparent #E91E63 !important; }

.loader-pink.ball-rotate div:before, .loader-pink.ball-rotate div:after {
  background-color: #E91E63 !important; }

.loader-pink.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #E91E63 !important; }

.loader-pink.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #E91E63 !important; }

.loader-pink.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #E91E63 !important; }

.loader-pink.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #E91E63 !important; }

.loader-pink.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #E91E63), to(#E91E63));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #E91E63 30%, #E91E63 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #E91E63 30%, #E91E63 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #E91E63 30%, #E91E63 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #E91E63 30%, #E91E63 100%);
  background-color: transparent !important; }

.loader-pink.fading-circle div {
  background-color: transparent !important; }
  .loader-pink.fading-circle div:before {
    background-color: #E91E63 !important; }

.loader-pink.folding-cube div {
  background-color: transparent !important; }
  .loader-pink.folding-cube div:before {
    background-color: #E91E63 !important; }

.loader-purple div {
  background-color: #9C27B0 !important; }

.loader-purple.ball-clip-rotate div {
  border-color: #9C27B0 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-purple.ball-clip-rotate-pulse div:first-child {
  background: #9C27B0 !important; }

.loader-purple.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #9C27B0 transparent #9C27B0 transparent !important; }

.loader-purple.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #9C27B0 transparent #9C27B0 !important; }

.loader-purple.ball-rotate div:before, .loader-purple.ball-rotate div:after {
  background-color: #9C27B0 !important; }

.loader-purple.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #9C27B0 !important; }

.loader-purple.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #9C27B0 !important; }

.loader-purple.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #9C27B0 !important; }

.loader-purple.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #9C27B0 !important; }

.loader-purple.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #9C27B0), to(#9C27B0));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #9C27B0 30%, #9C27B0 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #9C27B0 30%, #9C27B0 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #9C27B0 30%, #9C27B0 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #9C27B0 30%, #9C27B0 100%);
  background-color: transparent !important; }

.loader-purple.fading-circle div {
  background-color: transparent !important; }
  .loader-purple.fading-circle div:before {
    background-color: #9C27B0 !important; }

.loader-purple.folding-cube div {
  background-color: transparent !important; }
  .loader-purple.folding-cube div:before {
    background-color: #9C27B0 !important; }

.loader-blue div {
  background-color: #2196F3 !important; }

.loader-blue.ball-clip-rotate div {
  border-color: #2196F3 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-blue.ball-clip-rotate-pulse div:first-child {
  background: #2196F3 !important; }

.loader-blue.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #2196F3 transparent #2196F3 transparent !important; }

.loader-blue.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #2196F3 transparent #2196F3 !important; }

.loader-blue.ball-rotate div:before, .loader-blue.ball-rotate div:after {
  background-color: #2196F3 !important; }

.loader-blue.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #2196F3 !important; }

.loader-blue.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #2196F3 !important; }

.loader-blue.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #2196F3 !important; }

.loader-blue.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #2196F3 !important; }

.loader-blue.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #2196F3), to(#2196F3));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #2196F3 30%, #2196F3 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #2196F3 30%, #2196F3 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #2196F3 30%, #2196F3 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #2196F3 30%, #2196F3 100%);
  background-color: transparent !important; }

.loader-blue.fading-circle div {
  background-color: transparent !important; }
  .loader-blue.fading-circle div:before {
    background-color: #2196F3 !important; }

.loader-blue.folding-cube div {
  background-color: transparent !important; }
  .loader-blue.folding-cube div:before {
    background-color: #2196F3 !important; }

.loader-cyan div {
  background-color: #00BCD4 !important; }

.loader-cyan.ball-clip-rotate div {
  border-color: #00BCD4 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-cyan.ball-clip-rotate-pulse div:first-child {
  background: #00BCD4 !important; }

.loader-cyan.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #00BCD4 transparent #00BCD4 transparent !important; }

.loader-cyan.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #00BCD4 transparent #00BCD4 !important; }

.loader-cyan.ball-rotate div:before, .loader-cyan.ball-rotate div:after {
  background-color: #00BCD4 !important; }

.loader-cyan.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #00BCD4 !important; }

.loader-cyan.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #00BCD4 !important; }

.loader-cyan.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #00BCD4 !important; }

.loader-cyan.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #00BCD4 !important; }

.loader-cyan.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #00BCD4), to(#00BCD4));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #00BCD4 30%, #00BCD4 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #00BCD4 30%, #00BCD4 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #00BCD4 30%, #00BCD4 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #00BCD4 30%, #00BCD4 100%);
  background-color: transparent !important; }

.loader-cyan.fading-circle div {
  background-color: transparent !important; }
  .loader-cyan.fading-circle div:before {
    background-color: #00BCD4 !important; }

.loader-cyan.folding-cube div {
  background-color: transparent !important; }
  .loader-cyan.folding-cube div:before {
    background-color: #00BCD4 !important; }

.loader-teal div {
  background-color: #009688 !important; }

.loader-teal.ball-clip-rotate div {
  border-color: #009688 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-teal.ball-clip-rotate-pulse div:first-child {
  background: #009688 !important; }

.loader-teal.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #009688 transparent #009688 transparent !important; }

.loader-teal.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #009688 transparent #009688 !important; }

.loader-teal.ball-rotate div:before, .loader-teal.ball-rotate div:after {
  background-color: #009688 !important; }

.loader-teal.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #009688 !important; }

.loader-teal.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #009688 !important; }

.loader-teal.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #009688 !important; }

.loader-teal.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #009688 !important; }

.loader-teal.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #009688), to(#009688));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #009688 30%, #009688 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #009688 30%, #009688 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #009688 30%, #009688 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #009688 30%, #009688 100%);
  background-color: transparent !important; }

.loader-teal.fading-circle div {
  background-color: transparent !important; }
  .loader-teal.fading-circle div:before {
    background-color: #009688 !important; }

.loader-teal.folding-cube div {
  background-color: transparent !important; }
  .loader-teal.folding-cube div:before {
    background-color: #009688 !important; }

.loader-yellow div {
  background-color: #FFEB3B !important; }

.loader-yellow.ball-clip-rotate div {
  border-color: #FFEB3B !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-yellow.ball-clip-rotate-pulse div:first-child {
  background: #FFEB3B !important; }

.loader-yellow.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #FFEB3B transparent #FFEB3B transparent !important; }

.loader-yellow.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #FFEB3B transparent #FFEB3B !important; }

.loader-yellow.ball-rotate div:before, .loader-yellow.ball-rotate div:after {
  background-color: #FFEB3B !important; }

.loader-yellow.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #FFEB3B !important; }

.loader-yellow.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #FFEB3B !important; }

.loader-yellow.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #FFEB3B !important; }

.loader-yellow.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #FFEB3B !important; }

.loader-yellow.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #FFEB3B), to(#FFEB3B));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #FFEB3B 30%, #FFEB3B 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #FFEB3B 30%, #FFEB3B 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #FFEB3B 30%, #FFEB3B 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #FFEB3B 30%, #FFEB3B 100%);
  background-color: transparent !important; }

.loader-yellow.fading-circle div {
  background-color: transparent !important; }
  .loader-yellow.fading-circle div:before {
    background-color: #FFEB3B !important; }

.loader-yellow.folding-cube div {
  background-color: transparent !important; }
  .loader-yellow.folding-cube div:before {
    background-color: #FFEB3B !important; }

.loader-amber div {
  background-color: #FFC107 !important; }

.loader-amber.ball-clip-rotate div {
  border-color: #FFC107 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-amber.ball-clip-rotate-pulse div:first-child {
  background: #FFC107 !important; }

.loader-amber.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #FFC107 transparent #FFC107 transparent !important; }

.loader-amber.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #FFC107 transparent #FFC107 !important; }

.loader-amber.ball-rotate div:before, .loader-amber.ball-rotate div:after {
  background-color: #FFC107 !important; }

.loader-amber.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #FFC107 !important; }

.loader-amber.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #FFC107 !important; }

.loader-amber.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #FFC107 !important; }

.loader-amber.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #FFC107 !important; }

.loader-amber.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #FFC107), to(#FFC107));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #FFC107 30%, #FFC107 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #FFC107 30%, #FFC107 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #FFC107 30%, #FFC107 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #FFC107 30%, #FFC107 100%);
  background-color: transparent !important; }

.loader-amber.fading-circle div {
  background-color: transparent !important; }
  .loader-amber.fading-circle div:before {
    background-color: #FFC107 !important; }

.loader-amber.folding-cube div {
  background-color: transparent !important; }
  .loader-amber.folding-cube div:before {
    background-color: #FFC107 !important; }

.loader-blue-grey div {
  background-color: #607D8B !important; }

.loader-blue-grey.ball-clip-rotate div {
  border-color: #607D8B !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-blue-grey.ball-clip-rotate-pulse div:first-child {
  background: #607D8B !important; }

.loader-blue-grey.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #607D8B transparent #607D8B transparent !important; }

.loader-blue-grey.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #607D8B transparent #607D8B !important; }

.loader-blue-grey.ball-rotate div:before, .loader-blue-grey.ball-rotate div:after {
  background-color: #607D8B !important; }

.loader-blue-grey.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #607D8B !important; }

.loader-blue-grey.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #607D8B !important; }

.loader-blue-grey.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #607D8B !important; }

.loader-blue-grey.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #607D8B !important; }

.loader-blue-grey.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #607D8B), to(#607D8B));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #607D8B 30%, #607D8B 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #607D8B 30%, #607D8B 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #607D8B 30%, #607D8B 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #607D8B 30%, #607D8B 100%);
  background-color: transparent !important; }

.loader-blue-grey.fading-circle div {
  background-color: transparent !important; }
  .loader-blue-grey.fading-circle div:before {
    background-color: #607D8B !important; }

.loader-blue-grey.folding-cube div {
  background-color: transparent !important; }
  .loader-blue-grey.folding-cube div:before {
    background-color: #607D8B !important; }

.loader-grey-blue div {
  background-color: #1B2942 !important; }

.loader-grey-blue.ball-clip-rotate div {
  border-color: #1B2942 !important;
  border-bottom-color: transparent !important;
  background-color: transparent !important; }

.loader-grey-blue.ball-clip-rotate-pulse div:first-child {
  background: #1B2942 !important; }

.loader-grey-blue.ball-clip-rotate-pulse div:last-child {
  background: transparent !important;
  border-color: #1B2942 transparent #1B2942 transparent !important; }

.loader-grey-blue.ball-clip-rotate-multiple div {
  background-color: transparent !important;
  border-color: transparent #1B2942 transparent #1B2942 !important; }

.loader-grey-blue.ball-rotate div:before, .loader-grey-blue.ball-rotate div:after {
  background-color: #1B2942 !important; }

.loader-grey-blue.ball-triangle-path div {
  background-color: transparent !important;
  border-color: #1B2942 !important; }

.loader-grey-blue.ball-scale-ripple div {
  background-color: transparent !important;
  border-color: #1B2942 !important; }

.loader-grey-blue.ball-scale-ripple-multiple div {
  background-color: transparent !important;
  border-color: #1B2942 !important; }

.loader-grey-blue.triangle-skew-spin div {
  background-color: transparent !important;
  border-bottom-color: #1B2942 !important; }

.loader-grey-blue.semi-circle-spin div {
  background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(70%, transparent), color-stop(30%, #1B2942), to(#1B2942));
  background-image: -webkit-linear-gradient(transparent 0%, transparent 70%, #1B2942 30%, #1B2942 100%);
  background-image: -moz-linear-gradient(transparent 0%, transparent 70%, #1B2942 30%, #1B2942 100%);
  background-image: -o-linear-gradient(transparent 0%, transparent 70%, #1B2942 30%, #1B2942 100%);
  background-image: linear-gradient(transparent 0%, transparent 70%, #1B2942 30%, #1B2942 100%);
  background-color: transparent !important; }

.loader-grey-blue.fading-circle div {
  background-color: transparent !important; }
  .loader-grey-blue.fading-circle div:before {
    background-color: #1B2942 !important; }

.loader-grey-blue.folding-cube div {
  background-color: transparent !important; }
  .loader-grey-blue.folding-cube div:before {
    background-color: #1B2942 !important; }

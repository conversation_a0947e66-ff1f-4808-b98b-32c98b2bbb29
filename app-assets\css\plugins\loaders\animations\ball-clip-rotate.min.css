@-webkit-keyframes rotate{0%{-webkit-transform:rotate(0) scale(1);transform:rotate(0) scale(1)}50%{-webkit-transform:rotate(180deg) scale(.6);transform:rotate(180deg) scale(.6)}100%{-webkit-transform:rotate(360deg) scale(1);transform:rotate(360deg) scale(1)}}@-moz-keyframes rotate{0%{-moz-transform:rotate(0) scale(1);transform:rotate(0) scale(1)}50%{-moz-transform:rotate(180deg) scale(.6);transform:rotate(180deg) scale(.6)}100%{-moz-transform:rotate(360deg) scale(1);transform:rotate(360deg) scale(1)}}@-o-keyframes rotate{0%{-o-transform:rotate(0) scale(1);transform:rotate(0) scale(1)}50%{-o-transform:rotate(180deg) scale(.6);transform:rotate(180deg) scale(.6)}100%{-o-transform:rotate(360deg) scale(1);transform:rotate(360deg) scale(1)}}@keyframes rotate{0%{-webkit-transform:rotate(0) scale(1);-moz-transform:rotate(0) scale(1);-o-transform:rotate(0) scale(1);transform:rotate(0) scale(1)}50%{-webkit-transform:rotate(180deg) scale(.6);-moz-transform:rotate(180deg) scale(.6);-o-transform:rotate(180deg) scale(.6);transform:rotate(180deg) scale(.6)}100%{-webkit-transform:rotate(360deg) scale(1);-moz-transform:rotate(360deg) scale(1);-o-transform:rotate(360deg) scale(1);transform:rotate(360deg) scale(1)}}.ball-clip-rotate>div{display:inline-block;width:15px;width:25px;height:15px;height:25px;margin:2px;-webkit-animation:rotate .75s 0s linear infinite;-moz-animation:rotate .75s 0s linear infinite;-o-animation:rotate .75s 0s linear infinite;animation:rotate .75s 0s linear infinite;border:2px solid #404e67;border-bottom-color:transparent;border-radius:100%;background:0 0!important;-webkit-animation-fill-mode:both;-moz-animation-fill-mode:both;-o-animation-fill-mode:both;animation-fill-mode:both}
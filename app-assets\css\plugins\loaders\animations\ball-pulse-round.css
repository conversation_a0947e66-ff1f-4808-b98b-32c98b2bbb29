@-webkit-keyframes ball-pulse-round {
  0%, 80%, 100% {
    transform: scale(0);
    -webkit-transform: scale(0); }
  40% {
    transform: scale(1);
    -webkit-transform: scale(1); } }

@-moz-keyframes ball-pulse-round {
  0%, 80%, 100% {
    -moz-transform: scale(0);
    transform: scale(0);
    -webkit-transform: scale(0); }
  40% {
    -moz-transform: scale(1);
    transform: scale(1);
    -webkit-transform: scale(1); } }

@-o-keyframes ball-pulse-round {
  0%, 80%, 100% {
    -o-transform: scale(0);
    transform: scale(0);
    -webkit-transform: scale(0); }
  40% {
    -o-transform: scale(1);
    transform: scale(1);
    -webkit-transform: scale(1); } }

@keyframes ball-pulse-round {
  0%, 80%, 100% {
    -moz-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    -webkit-transform: scale(0); }
  40% {
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    -webkit-transform: scale(1); } }

.ball-pulse-round > div {
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  width: 10px;
  height: 10px;
  -webkit-animation: ball-pulse-round 1.2s infinite ease-in-out;
  -moz-animation: ball-pulse-round 1.2s infinite ease-in-out;
  -o-animation: ball-pulse-round 1.2s infinite ease-in-out;
  animation: ball-pulse-round 1.2s infinite ease-in-out; }

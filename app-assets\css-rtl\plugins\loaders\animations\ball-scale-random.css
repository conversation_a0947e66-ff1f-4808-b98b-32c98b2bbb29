@-webkit-keyframes ball-scale
{
    0%
    {
        -webkit-transform: scale(0);
                transform: scale(0);
    }
    100%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: 0;
    }
}

@-moz-keyframes ball-scale
{
    0%
    {
        -moz-transform: scale(0);
             transform: scale(0);
    }
    100%
    {
        -moz-transform: scale(1);
             transform: scale(1);

        opacity: 0;
    }
}

@-o-keyframes ball-scale
{
    0%
    {
        -o-transform: scale(0);
           transform: scale(0);
    }
    100%
    {
        -o-transform: scale(1);
           transform: scale(1);

        opacity: 0;
    }
}

@keyframes ball-scale
{
    0%
    {
        -webkit-transform: scale(0);
           -moz-transform: scale(0);
             -o-transform: scale(0);
                transform: scale(0);
    }
    100%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);

        opacity: 0;
    }
}

.ball-scale > div
{
    display: inline-block;

    width: 15px;
    width: 60px;
    height: 15px;
    height: 60px;
    margin: 2px;

    -webkit-animation: ball-scale 1s 0s ease-in-out infinite;
       -moz-animation: ball-scale 1s 0s ease-in-out infinite;
         -o-animation: ball-scale 1s 0s ease-in-out infinite;
            animation: ball-scale 1s 0s ease-in-out infinite; 

    border-radius: 100%;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

.ball-scale-random
{
    width: 37px;
    height: 40px;
}
.ball-scale-random > div
{
    position: absolute;

    display: inline-block;

    width: 15px;
    width: 30px;
    height: 15px;
    height: 30px;
    margin: 2px;

    -webkit-animation: ball-scale 1s 0s ease-in-out infinite;
       -moz-animation: ball-scale 1s 0s ease-in-out infinite;
         -o-animation: ball-scale 1s 0s ease-in-out infinite;
            animation: ball-scale 1s 0s ease-in-out infinite; 

    border-radius: 100%;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}
.ball-scale-random > div:nth-child(1)
{
    margin-right: -7px;

    -webkit-animation: ball-scale 1s .2s ease-in-out infinite;
       -moz-animation: ball-scale 1s .2s ease-in-out infinite;
         -o-animation: ball-scale 1s .2s ease-in-out infinite;
            animation: ball-scale 1s .2s ease-in-out infinite;
}
.ball-scale-random > div:nth-child(3)
{
    margin-top: 9px;
    margin-right: -2px;

    -webkit-animation: ball-scale 1s .5s ease-in-out infinite;
       -moz-animation: ball-scale 1s .5s ease-in-out infinite;
         -o-animation: ball-scale 1s .5s ease-in-out infinite;
            animation: ball-scale 1s .5s ease-in-out infinite;
}

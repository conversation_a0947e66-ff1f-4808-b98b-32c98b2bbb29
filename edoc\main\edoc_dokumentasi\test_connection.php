<?php
echo "<h2>Test Koneksi Database & Sistem</h2>";

// Test koneksi database
include '../../config.php';

if ($koneksi) {
    echo "<p style='color: green;'>✓ Koneksi database berhasil</p>";
    
    // Test query tabel yang diperlukan
    $tables = [
        'edoc_refrensi' => 'SELECT COUNT(*) as total FROM edoc_refrensi',
        'unit' => 'SELECT COUNT(*) as total FROM unit',
        'edoc_admin' => 'SELECT COUNT(*) as total FROM edoc_admin',
        'edoc_dokumentasi' => 'SELECT COUNT(*) as total FROM edoc_dokumentasi',
        'edoc_dokumentasi_detail' => 'SELECT COUNT(*) as total FROM edoc_dokumentasi_detail'
    ];
    
    echo "<h3>Status Tabel:</h3>";
    foreach ($tables as $tableName => $query) {
        $result = mysqli_query($koneksi, $query);
        if ($result) {
            $count = mysqli_fetch_array($result)['total'];
            echo "<p style='color: green;'>✓ Tabel $tableName: $count records</p>";
        } else {
            echo "<p style='color: red;'>✗ Tabel $tableName: " . mysqli_error($koneksi) . "</p>";
        }
    }
    
    // Test data referensi
    echo "<h3>Data Referensi:</h3>";
    $refResult = mysqli_query($koneksi, "SELECT JENIS, COUNT(*) as total FROM edoc_refrensi GROUP BY JENIS");
    if ($refResult) {
        while ($row = mysqli_fetch_array($refResult)) {
            $jenisName = ($row['JENIS'] == 1) ? 'Kategori Dokumentasi' : 'Jenis Dokumentasi';
            echo "<p style='color: blue;'>→ $jenisName: {$row['total']} items</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>✗ Koneksi database gagal: " . mysqli_connect_error() . "</p>";
}

// Test folder permissions
echo "<h3>Status Folder:</h3>";

$folders = [
    '../../../edoc/main/file_dokumentasi/' => 'Folder Upload Dokumentasi',
    '../../assets/images/' => 'Folder Assets Images'
];

foreach ($folders as $path => $description) {
    if (file_exists($path)) {
        if (is_writable($path)) {
            echo "<p style='color: green;'>✓ $description: Writable</p>";
        } else {
            echo "<p style='color: orange;'>⚠ $description: Not Writable</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ $description: Folder tidak ada</p>";
        // Coba buat folder
        if (mkdir($path, 0755, true)) {
            echo "<p style='color: green;'>✓ $description: Berhasil dibuat</p>";
        } else {
            echo "<p style='color: red;'>✗ $description: Gagal dibuat</p>";
        }
    }
}

// Test file logo
$logoPath = '../../assets/images/logo.png';
if (file_exists($logoPath)) {
    echo "<p style='color: green;'>✓ Logo file: Ada</p>";
    $imageInfo = getimagesize($logoPath);
    if ($imageInfo) {
        echo "<p style='color: blue;'>→ Ukuran logo: {$imageInfo[0]}x{$imageInfo[1]} pixels</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Logo file: Tidak ada di $logoPath</p>";
}

// Test PHP extensions
echo "<h3>PHP Extensions:</h3>";
$extensions = ['gd', 'mysqli', 'json'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext: Loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext: Not Loaded</p>";
    }
}

// Test GD functions
if (extension_loaded('gd')) {
    $gdInfo = gd_info();
    echo "<p style='color: blue;'>→ GD Version: {$gdInfo['GD Version']}</p>";
    echo "<p style='color: blue;'>→ JPEG Support: " . ($gdInfo['JPEG Support'] ? 'Yes' : 'No') . "</p>";
    echo "<p style='color: blue;'>→ PNG Support: " . ($gdInfo['PNG Support'] ? 'Yes' : 'No') . "</p>";
}

echo "<h3>Session Test:</h3>";
session_start();
if (isset($_SESSION)) {
    echo "<p style='color: green;'>✓ Session: Working</p>";
    if (isset($_SESSION['username'])) {
        echo "<p style='color: blue;'>→ Current User: {$_SESSION['username']}</p>";
        echo "<p style='color: blue;'>→ Hak Akses: {$_SESSION['hak_akses']}</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Session: No user logged in</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Session: Not working</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Kembali ke Dokumentasi Kegiatan</a></p>";
echo "<p><a href='install_tables.php'>→ Install/Update Tables</a></p>";
?>

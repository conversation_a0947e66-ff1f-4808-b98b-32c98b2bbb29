<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Fungsi untuk membuat nama folder yang aman
function createSafeFileName($string) {
    $string = preg_replace('/[^A-Za-z0-9\-_]/', '_', $string);
    $string = preg_replace('/_+/', '_', $string);
    $string = trim($string, '_');
    return $string;
}

try {
    // Validasi input
    if (empty($_POST['id']) || empty($_POST['judul']) || empty($_POST['id_kategori']) || 
        empty($_POST['tanggal']) || empty($_POST['lokasi']) || empty($_POST['unit']) || 
        empty($_POST['id_jenis_dok'])) {
        throw new Exception('Semua field wajib harus diisi');
    }

    $id = intval($_POST['id']);
    $judul = mysqli_real_escape_string($koneksi, $_POST['judul']);
    $id_kategori = intval($_POST['id_kategori']);
    $tanggal = mysqli_real_escape_string($koneksi, $_POST['tanggal']);
    $lokasi = mysqli_real_escape_string($koneksi, $_POST['lokasi']);
    $unit = mysqli_real_escape_string($koneksi, $_POST['unit']);
    $id_jenis_dok = intval($_POST['id_jenis_dok']);
    $keterangan = mysqli_real_escape_string($koneksi, $_POST['keterangan']);

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND UNIT = '$user'";
    }

    // Cek apakah data ada dan user memiliki akses
    $sqlCheck = "SELECT * FROM edoc_dokumentasi WHERE ID = $id AND STATUS = 1 $whereAccess";
    $resultCheck = mysqli_query($koneksi, $sqlCheck);

    if (!$resultCheck || mysqli_num_rows($resultCheck) == 0) {
        throw new Exception('Data dokumentasi tidak ditemukan atau Anda tidak memiliki akses');
    }

    $existingData = mysqli_fetch_array($resultCheck);

    // Mulai transaksi
    mysqli_autocommit($koneksi, false);

    // Update data utama dokumentasi
    $sqlUpdate = "UPDATE edoc_dokumentasi SET 
                  JUDUL = '$judul',
                  ID_KATEGORI = $id_kategori,
                  TANGGAL = '$tanggal',
                  LOKASI = '$lokasi',
                  UNIT = '$unit',
                  ID_JENIS_DOK = $id_jenis_dok,
                  KETERANGAN = '$keterangan'
                  WHERE ID = $id";

    if (!mysqli_query($koneksi, $sqlUpdate)) {
        throw new Exception('Gagal mengupdate data dokumentasi: ' . mysqli_error($koneksi));
    }

    $uploadedFiles = 0;

    // Cek apakah ada file baru yang diupload
    $fotoFiles = isset($_FILES['foto_baru']) ? $_FILES['foto_baru'] : null;
    $videoUrls = isset($_POST['video_baru']) ? array_filter($_POST['video_baru']) : [];

    if (($fotoFiles && count($fotoFiles['name']) > 0 && !empty($fotoFiles['name'][0])) || 
        (!empty($videoUrls))) {
        
        // Gunakan folder path yang sudah ada
        $folderPath = $existingData['FILE_PATH'];
        $uploadPath = '../../../edoc/main' . $folderPath;

        // Pastikan folder masih ada
        if (!file_exists($uploadPath)) {
            if (!mkdir($uploadPath, 0755, true)) {
                throw new Exception('Gagal membuat folder upload');
            }
        }

        // Proses upload foto baru
        if ($fotoFiles && count($fotoFiles['name']) > 0 && !empty($fotoFiles['name'][0])) {
            for ($i = 0; $i < count($fotoFiles['name']); $i++) {
                if ($fotoFiles['error'][$i] == UPLOAD_ERR_OK) {
                    $fileName = $fotoFiles['name'][$i];
                    $fileTmp = $fotoFiles['tmp_name'][$i];
                    $fileSize = $fotoFiles['size'][$i];
                    $fileType = $fotoFiles['type'][$i];

                    // Validasi tipe file
                    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!in_array($fileType, $allowedTypes)) {
                        continue;
                    }

                    // Validasi ukuran file (max 5MB)
                    if ($fileSize > 5 * 1024 * 1024) {
                        continue;
                    }

                    // Buat nama file baru dengan timestamp untuk menghindari duplikasi
                    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
                    $baseName = pathinfo($fileName, PATHINFO_FILENAME);
                    $newFileName = createSafeFileName($baseName) . '_' . time() . '_' . ($i + 1) . '.' . $fileExtension;
                    $filePath = $uploadPath . '/' . $newFileName;

                    // Upload file
                    if (move_uploaded_file($fileTmp, $filePath)) {
                        // Insert ke database
                        $sqlDetail = "INSERT INTO edoc_dokumentasi_detail 
                                      (ID_DOKUMENTASI, FILE_PATH, TIPE_FILE, LOKASI_FILE, UPLOADED_AT, STATUS) 
                                      VALUES 
                                      ($id, '$newFileName', 'foto', '$folderPath', NOW(), 1)";
                        
                        if (mysqli_query($koneksi, $sqlDetail)) {
                            $uploadedFiles++;
                        }
                    }
                }
            }
        }

        // Proses video URLs baru
        if (!empty($videoUrls)) {
            foreach ($videoUrls as $index => $videoUrl) {
                if (!empty(trim($videoUrl))) {
                    $cleanUrl = mysqli_real_escape_string($koneksi, trim($videoUrl));
                    
                    // Validasi URL (basic)
                    if (filter_var($cleanUrl, FILTER_VALIDATE_URL)) {
                        $sqlDetail = "INSERT INTO edoc_dokumentasi_detail 
                                      (ID_DOKUMENTASI, FILE_PATH, TIPE_FILE, LOKASI_FILE, UPLOADED_AT, STATUS) 
                                      VALUES 
                                      ($id, '$cleanUrl', 'video', '$folderPath', NOW(), 1)";
                        
                        if (mysqli_query($koneksi, $sqlDetail)) {
                            $uploadedFiles++;
                        }
                    }
                }
            }
        }
    }

    // Commit transaksi
    mysqli_commit($koneksi);
    mysqli_autocommit($koneksi, true);

    $message = 'Dokumentasi berhasil diupdate';
    if ($uploadedFiles > 0) {
        $message .= ' dengan ' . $uploadedFiles . ' file baru';
    }

    echo json_encode([
        'status' => 'success', 
        'message' => $message
    ]);

} catch (Exception $e) {
    // Rollback transaksi jika ada error
    mysqli_rollback($koneksi);
    mysqli_autocommit($koneksi, true);
    
    echo json_encode([
        'status' => 'error', 
        'message' => $e->getMessage()
    ]);
}
?>

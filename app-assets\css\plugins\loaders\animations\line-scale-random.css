@-webkit-keyframes line-scale-party {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

@-moz-keyframes line-scale-party {
  0% {
    -moz-transform: scale(1);
    transform: scale(1); }
  50% {
    -moz-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -moz-transform: scale(1);
    transform: scale(1); } }

@-o-keyframes line-scale-party {
  0% {
    -o-transform: scale(1);
    transform: scale(1); }
  50% {
    -o-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -o-transform: scale(1);
    transform: scale(1); } }

@keyframes line-scale-party {
  0% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.5);
    -moz-transform: scale(0.5);
    -o-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }

.line-scale-party > div:nth-child(1) {
  -webkit-animation-delay: -0.01s;
  -moz-animation-delay: -0.01s;
  -o-animation-delay: -0.01s;
  animation-delay: -0.01s;
  -webkit-animation-duration: 1.12s;
  -moz-animation-duration: 1.12s;
  -o-animation-duration: 1.12s;
  animation-duration: 1.12s; }

.line-scale-party > div:nth-child(2) {
  -webkit-animation-delay: -0.1s;
  -moz-animation-delay: -0.1s;
  -o-animation-delay: -0.1s;
  animation-delay: -0.1s;
  -webkit-animation-duration: 1.12s;
  -moz-animation-duration: 1.12s;
  -o-animation-duration: 1.12s;
  animation-duration: 1.12s; }

.line-scale-party > div:nth-child(3) {
  -webkit-animation-delay: 0.17s;
  -moz-animation-delay: 0.17s;
  -o-animation-delay: 0.17s;
  animation-delay: 0.17s;
  -webkit-animation-duration: 0.78s;
  -moz-animation-duration: 0.78s;
  -o-animation-duration: 0.78s;
  animation-duration: 0.78s; }

.line-scale-party > div:nth-child(4) {
  -webkit-animation-delay: 0.69s;
  -moz-animation-delay: 0.69s;
  -o-animation-delay: 0.69s;
  animation-delay: 0.69s;
  -webkit-animation-duration: 0.79s;
  -moz-animation-duration: 0.79s;
  -o-animation-duration: 0.79s;
  animation-duration: 0.79s; }

.line-scale-party > div {
  background-color: #404E67;
  width: 4px;
  height: 3.45rem;
  border-radius: 2px;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block;
  -webkit-animation-name: line-scale-party;
  -moz-animation-name: line-scale-party;
  -o-animation-name: line-scale-party;
  animation-name: line-scale-party;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  -o-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-delay: 0;
  -moz-animation-delay: 0;
  -o-animation-delay: 0;
  animation-delay: 0; }

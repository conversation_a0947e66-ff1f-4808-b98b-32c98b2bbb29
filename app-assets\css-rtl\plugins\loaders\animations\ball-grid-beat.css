@-webkit-keyframes ball-grid-beat
{
    50%
    {
        opacity: .7;
    }
    100%
    {
        opacity: 1;
    }
}

@-moz-keyframes ball-grid-beat
{
    50%
    {
        opacity: .7;
    }
    100%
    {
        opacity: 1;
    }
}

@-o-keyframes ball-grid-beat
{
    50%
    {
        opacity: .7;
    }
    100%
    {
        opacity: 1;
    }
}

@keyframes ball-grid-beat
{
    50%
    {
        opacity: .7;
    }
    100%
    {
        opacity: 1;
    }
}

.ball-grid-beat
{
    width: 57px;
}
.ball-grid-beat > div:nth-child(1)
{
    -webkit-animation-duration: .66s;
       -moz-animation-duration: .66s;
         -o-animation-duration: .66s;
            animation-duration: .66s; 
    -webkit-animation-delay: .56s;
       -moz-animation-delay: .56s;
         -o-animation-delay: .56s;
            animation-delay: .56s;
}
.ball-grid-beat > div:nth-child(2)
{
    -webkit-animation-duration: 1.55s;
       -moz-animation-duration: 1.55s;
         -o-animation-duration: 1.55s;
            animation-duration: 1.55s; 
    -webkit-animation-delay: .77s;
       -moz-animation-delay: .77s;
         -o-animation-delay: .77s;
            animation-delay: .77s;
}
.ball-grid-beat > div:nth-child(3)
{
    -webkit-animation-duration: 1.32s;
       -moz-animation-duration: 1.32s;
         -o-animation-duration: 1.32s;
            animation-duration: 1.32s; 
    -webkit-animation-delay: .48s;
       -moz-animation-delay: .48s;
         -o-animation-delay: .48s;
            animation-delay: .48s;
}
.ball-grid-beat > div:nth-child(4)
{
    -webkit-animation-duration: .84s;
       -moz-animation-duration: .84s;
         -o-animation-duration: .84s;
            animation-duration: .84s; 
    -webkit-animation-delay: .6s;
       -moz-animation-delay: .6s;
         -o-animation-delay: .6s;
            animation-delay: .6s;
}
.ball-grid-beat > div:nth-child(5)
{
    -webkit-animation-duration: 1.54s;
       -moz-animation-duration: 1.54s;
         -o-animation-duration: 1.54s;
            animation-duration: 1.54s; 
    -webkit-animation-delay: .04s;
       -moz-animation-delay: .04s;
         -o-animation-delay: .04s;
            animation-delay: .04s;
}
.ball-grid-beat > div:nth-child(6)
{
    -webkit-animation-duration: 1.4s;
       -moz-animation-duration: 1.4s;
         -o-animation-duration: 1.4s;
            animation-duration: 1.4s; 
    -webkit-animation-delay: .68s;
       -moz-animation-delay: .68s;
         -o-animation-delay: .68s;
            animation-delay: .68s;
}
.ball-grid-beat > div:nth-child(7)
{
    -webkit-animation-duration: 1.11s;
       -moz-animation-duration: 1.11s;
         -o-animation-duration: 1.11s;
            animation-duration: 1.11s; 
    -webkit-animation-delay: .42s;
       -moz-animation-delay: .42s;
         -o-animation-delay: .42s;
            animation-delay: .42s;
}
.ball-grid-beat > div:nth-child(8)
{
    -webkit-animation-duration: 1.2s;
       -moz-animation-duration: 1.2s;
         -o-animation-duration: 1.2s;
            animation-duration: 1.2s; 
    -webkit-animation-delay: .11s;
       -moz-animation-delay: .11s;
         -o-animation-delay: .11s;
            animation-delay: .11s;
}
.ball-grid-beat > div:nth-child(9)
{
    -webkit-animation-duration: 1.6s;
       -moz-animation-duration: 1.6s;
         -o-animation-duration: 1.6s;
            animation-duration: 1.6s; 
    -webkit-animation-delay: .66s;
       -moz-animation-delay: .66s;
         -o-animation-delay: .66s;
            animation-delay: .66s;
}
.ball-grid-beat > div
{
    display: inline-block;
    float: right;

    width: 15px;
    height: 15px;
    margin: 2px;

    -webkit-animation-name: ball-grid-beat;
       -moz-animation-name: ball-grid-beat;
         -o-animation-name: ball-grid-beat;
            animation-name: ball-grid-beat;
    -webkit-animation-delay: 0;
       -moz-animation-delay: 0;
         -o-animation-delay: 0;
            animation-delay: 0; 
    -webkit-animation-iteration-count: infinite;
       -moz-animation-iteration-count: infinite;
         -o-animation-iteration-count: infinite;
            animation-iteration-count: infinite;

    border-radius: 100%;
    background-color: #404e67;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

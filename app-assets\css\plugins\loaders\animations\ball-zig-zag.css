@-webkit-keyframes ball-zig {
  33% {
    -webkit-transform: translate(-15px, -30px);
    transform: translate(-15px, -30px); }
  66% {
    -webkit-transform: translate(15px, -30px);
    transform: translate(15px, -30px); }
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0); } }

@-moz-keyframes ball-zig {
  33% {
    -moz-transform: translate(-15px, -30px);
    transform: translate(-15px, -30px); }
  66% {
    -moz-transform: translate(15px, -30px);
    transform: translate(15px, -30px); }
  100% {
    -moz-transform: translate(0, 0);
    transform: translate(0, 0); } }

@-o-keyframes ball-zig {
  33% {
    -o-transform: translate(-15px, -30px);
    transform: translate(-15px, -30px); }
  66% {
    -o-transform: translate(15px, -30px);
    transform: translate(15px, -30px); }
  100% {
    -o-transform: translate(0, 0);
    transform: translate(0, 0); } }

@keyframes ball-zig {
  33% {
    -webkit-transform: translate(-15px, -30px);
    -moz-transform: translate(-15px, -30px);
    -o-transform: translate(-15px, -30px);
    transform: translate(-15px, -30px); }
  66% {
    -webkit-transform: translate(15px, -30px);
    -moz-transform: translate(15px, -30px);
    -o-transform: translate(15px, -30px);
    transform: translate(15px, -30px); }
  100% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0); } }

@-webkit-keyframes ball-zag {
  33% {
    -webkit-transform: translate(15px, 30px);
    transform: translate(15px, 30px); }
  66% {
    -webkit-transform: translate(-15px, 30px);
    transform: translate(-15px, 30px); }
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0); } }

@-moz-keyframes ball-zag {
  33% {
    -moz-transform: translate(15px, 30px);
    transform: translate(15px, 30px); }
  66% {
    -moz-transform: translate(-15px, 30px);
    transform: translate(-15px, 30px); }
  100% {
    -moz-transform: translate(0, 0);
    transform: translate(0, 0); } }

@-o-keyframes ball-zag {
  33% {
    -o-transform: translate(15px, 30px);
    transform: translate(15px, 30px); }
  66% {
    -o-transform: translate(-15px, 30px);
    transform: translate(-15px, 30px); }
  100% {
    -o-transform: translate(0, 0);
    transform: translate(0, 0); } }

@keyframes ball-zag {
  33% {
    -webkit-transform: translate(15px, 30px);
    -moz-transform: translate(15px, 30px);
    -o-transform: translate(15px, 30px);
    transform: translate(15px, 30px); }
  66% {
    -webkit-transform: translate(-15px, 30px);
    -moz-transform: translate(-15px, 30px);
    -o-transform: translate(-15px, 30px);
    transform: translate(-15px, 30px); }
  100% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0); } }

.ball-zig-zag {
  position: relative;
  -webkit-transform: translate(-15px, -15px);
  -moz-transform: translate(-15px, -15px);
  -ms-transform: translate(-15px, -15px);
  -o-transform: translate(-15px, -15px);
  transform: translate(-15px, -15px); }
  .ball-zig-zag > div {
    background-color: #404E67;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    margin: 2px;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    margin-left: 15px;
    top: 4px;
    left: -7px; }
    .ball-zig-zag > div:first-child {
      -webkit-animation: ball-zig 0.7s 0s infinite linear;
      -moz-animation: ball-zig 0.7s 0s infinite linear;
      -o-animation: ball-zig 0.7s 0s infinite linear;
      animation: ball-zig 0.7s 0s infinite linear; }
    .ball-zig-zag > div:last-child {
      -webkit-animation: ball-zag 0.7s 0s infinite linear;
      -moz-animation: ball-zag 0.7s 0s infinite linear;
      -o-animation: ball-zag 0.7s 0s infinite linear;
      animation: ball-zag 0.7s 0s infinite linear; }

@-webkit-keyframes ball-beat {
  50% {
    opacity: 0.2;
    -webkit-transform: scale(0.75);
    transform: scale(0.75); }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1); } }

@-moz-keyframes ball-beat {
  50% {
    opacity: 0.2;
    -moz-transform: scale(0.75);
    transform: scale(0.75); }
  100% {
    opacity: 1;
    -moz-transform: scale(1);
    transform: scale(1); } }

@-o-keyframes ball-beat {
  50% {
    opacity: 0.2;
    -o-transform: scale(0.75);
    transform: scale(0.75); }
  100% {
    opacity: 1;
    -o-transform: scale(1);
    transform: scale(1); } }

@keyframes ball-beat {
  50% {
    opacity: 0.2;
    -webkit-transform: scale(0.75);
    -moz-transform: scale(0.75);
    -o-transform: scale(0.75);
    transform: scale(0.75); }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1); } }

.ball-beat > div {
  background-color: #404E67;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block;
  -webkit-animation: ball-beat 0.7s 0s infinite linear;
  -moz-animation: ball-beat 0.7s 0s infinite linear;
  -o-animation: ball-beat 0.7s 0s infinite linear;
  animation: ball-beat 0.7s 0s infinite linear; }
  .ball-beat > div:nth-child(2n-1) {
    -webkit-animation-delay: -0.35s !important;
    -moz-animation-delay: -0.35s !important;
    -o-animation-delay: -0.35s !important;
    animation-delay: -0.35s !important; }

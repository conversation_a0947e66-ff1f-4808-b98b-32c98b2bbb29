@-webkit-keyframes line-scale-pulse-out {
  0% {
    -webkit-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -webkit-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -webkit-transform: scaley(1);
    transform: scaley(1); } }

@-moz-keyframes line-scale-pulse-out {
  0% {
    -moz-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -moz-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -moz-transform: scaley(1);
    transform: scaley(1); } }

@-o-keyframes line-scale-pulse-out {
  0% {
    -o-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -o-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -o-transform: scaley(1);
    transform: scaley(1); } }

@keyframes line-scale-pulse-out {
  0% {
    -webkit-transform: scaley(1);
    -moz-transform: scaley(1);
    -o-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -webkit-transform: scaley(0.4);
    -moz-transform: scaley(0.4);
    -o-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -webkit-transform: scaley(1);
    -moz-transform: scaley(1);
    -o-transform: scaley(1);
    transform: scaley(1); } }

.line-scale-pulse-out > div {
  background-color: #404E67;
  width: 4px;
  height: 3.45rem;
  border-radius: 2px;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block;
  -webkit-animation: line-scale-pulse-out 0.9s -0.6s infinite cubic-bezier(0.85, 0.25, 0.37, 0.85);
  -moz-animation: line-scale-pulse-out 0.9s -0.6s infinite cubic-bezier(0.85, 0.25, 0.37, 0.85);
  -o-animation: line-scale-pulse-out 0.9s -0.6s infinite cubic-bezier(0.85, 0.25, 0.37, 0.85);
  animation: line-scale-pulse-out 0.9s -0.6s infinite cubic-bezier(0.85, 0.25, 0.37, 0.85); }
  .line-scale-pulse-out > div:nth-child(2), .line-scale-pulse-out > div:nth-child(4) {
    -webkit-animation-delay: -0.4s !important;
    -moz-animation-delay: -0.4s !important;
    -o-animation-delay: -0.4s !important;
    animation-delay: -0.4s !important; }
  .line-scale-pulse-out > div:nth-child(1), .line-scale-pulse-out > div:nth-child(5) {
    -webkit-animation-delay: -0.2s !important;
    -moz-animation-delay: -0.2s !important;
    -o-animation-delay: -0.2s !important;
    animation-delay: -0.2s !important; }

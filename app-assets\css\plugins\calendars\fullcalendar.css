.btn, .fc button {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out; }
  @media screen and (prefers-reduced-motion: reduce) {
    .btn, .fc button {
      -webkit-transition: none;
      -o-transition: none;
      -moz-transition: none;
      transition: none; } }
  .btn:hover, .fc button:hover, .btn:focus, .fc button:focus {
    text-decoration: none; }
  .btn:focus, .fc button:focus, .btn.focus, .fc button.focus {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn.disabled, .fc button.disabled, .btn:disabled, .fc button:disabled {
    opacity: 0.65; }
  .btn:not(:disabled):not(.disabled), .fc button:not(:disabled):not(.disabled) {
    cursor: pointer; }

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none; }

.btn-primary {
  color: #fff;
  background-color: #00B5B8;
  border-color: #00B5B8; }
  .btn-primary:hover {
    color: #fff;
    background-color: #008f92;
    border-color: #008385; }
  .btn-primary:focus, .btn-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5); }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #00B5B8;
    border-color: #00B5B8; }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
  .show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #008385;
    border-color: #007678; }
    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5); }

.btn-secondary {
  color: #fff;
  background-color: #404E67;
  border-color: #404E67; }
  .btn-secondary:hover {
    color: #fff;
    background-color: #313c4f;
    border-color: #2c3648; }
  .btn-secondary:focus, .btn-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5); }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #404E67;
    border-color: #404E67; }
  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #2c3648;
    border-color: #283040; }
    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5); }

.btn-success {
  color: #fff;
  background-color: #16D39A;
  border-color: #16D39A; }
  .btn-success:hover {
    color: #fff;
    background-color: #12b081;
    border-color: #11a578; }
  .btn-success:focus, .btn-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5); }
  .btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #16D39A;
    border-color: #16D39A; }
  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
  .show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #11a578;
    border-color: #109970; }
    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5); }

.btn-info {
  color: #2A2E30;
  background-color: #2DCEE3;
  border-color: #2DCEE3; }
  .btn-info:hover {
    color: #fff;
    background-color: #1cbace;
    border-color: #1ab0c3; }
  .btn-info:focus, .btn-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5); }
  .btn-info.disabled, .btn-info:disabled {
    color: #2A2E30;
    background-color: #2DCEE3;
    border-color: #2DCEE3; }
  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
  .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #1ab0c3;
    border-color: #19a5b8; }
    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5); }

.btn-warning {
  color: #2A2E30;
  background-color: #FFA87D;
  border-color: #FFA87D; }
  .btn-warning:hover {
    color: #2A2E30;
    background-color: #ff8e57;
    border-color: #ff864a; }
  .btn-warning:focus, .btn-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5); }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #2A2E30;
    background-color: #FFA87D;
    border-color: #FFA87D; }
  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
  .show > .btn-warning.dropdown-toggle {
    color: #2A2E30;
    background-color: #ff864a;
    border-color: #ff7d3d; }
    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5); }

.btn-danger {
  color: #2A2E30;
  background-color: #FF7588;
  border-color: #FF7588; }
  .btn-danger:hover {
    color: #fff;
    background-color: #ff4f67;
    border-color: #ff425c; }
  .btn-danger:focus, .btn-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5); }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #2A2E30;
    background-color: #FF7588;
    border-color: #FF7588; }
  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
  .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #ff425c;
    border-color: #ff3551; }
    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5); }

.btn-light {
  color: #2A2E30;
  background-color: #BABFC7;
  border-color: #BABFC7; }
  .btn-light:hover {
    color: #2A2E30;
    background-color: #a5abb6;
    border-color: #9ea5b0; }
  .btn-light:focus, .btn-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5); }
  .btn-light.disabled, .btn-light:disabled {
    color: #2A2E30;
    background-color: #BABFC7;
    border-color: #BABFC7; }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
  .show > .btn-light.dropdown-toggle {
    color: #2A2E30;
    background-color: #9ea5b0;
    border-color: #979eaa; }
    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5); }

.btn-dark {
  color: #fff;
  background-color: #1B2942;
  border-color: #1B2942; }
  .btn-dark:hover {
    color: #fff;
    background-color: #101827;
    border-color: #0c131e; }
  .btn-dark:focus, .btn-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5); }
  .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #1B2942;
    border-color: #1B2942; }
  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
  .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #0c131e;
    border-color: #080d15; }
    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5); }

.btn-outline-primary, .fc button {
  color: #00B5B8;
  background-color: transparent;
  background-image: none;
  border-color: #00B5B8; }
  .btn-outline-primary:hover, .fc button:hover {
    color: #fff;
    background-color: #00B5B8;
    border-color: #00B5B8; }
  .btn-outline-primary:focus, .fc button:focus, .btn-outline-primary.focus, .fc button.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5); }
  .btn-outline-primary.disabled, .fc button.disabled, .btn-outline-primary:disabled, .fc button:disabled {
    color: #00B5B8;
    background-color: transparent; }
  .btn-outline-primary:not(:disabled):not(.disabled):active, .fc button:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .fc button:not(:disabled):not(.disabled).active,
  .show > .btn-outline-primary.dropdown-toggle,
  .fc .show > button.dropdown-toggle {
    color: #fff;
    background-color: #00B5B8;
    border-color: #00B5B8; }
    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .fc button:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .fc button:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-primary.dropdown-toggle:focus,
    .fc .show > button.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(0, 181, 184, 0.5); }

.btn-outline-secondary {
  color: #404E67;
  background-color: transparent;
  background-image: none;
  border-color: #404E67; }
  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #404E67;
    border-color: #404E67; }
  .btn-outline-secondary:focus, .btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5); }
  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #404E67;
    background-color: transparent; }
  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #404E67;
    border-color: #404E67; }
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(64, 78, 103, 0.5); }

.btn-outline-success {
  color: #16D39A;
  background-color: transparent;
  background-image: none;
  border-color: #16D39A; }
  .btn-outline-success:hover {
    color: #fff;
    background-color: #16D39A;
    border-color: #16D39A; }
  .btn-outline-success:focus, .btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5); }
  .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #16D39A;
    background-color: transparent; }
  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
  .show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #16D39A;
    border-color: #16D39A; }
    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(22, 211, 154, 0.5); }

.btn-outline-info {
  color: #2DCEE3;
  background-color: transparent;
  background-image: none;
  border-color: #2DCEE3; }
  .btn-outline-info:hover {
    color: #2A2E30;
    background-color: #2DCEE3;
    border-color: #2DCEE3; }
  .btn-outline-info:focus, .btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5); }
  .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #2DCEE3;
    background-color: transparent; }
  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
  .show > .btn-outline-info.dropdown-toggle {
    color: #2A2E30;
    background-color: #2DCEE3;
    border-color: #2DCEE3; }
    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(45, 206, 227, 0.5); }

.btn-outline-warning {
  color: #FFA87D;
  background-color: transparent;
  background-image: none;
  border-color: #FFA87D; }
  .btn-outline-warning:hover {
    color: #2A2E30;
    background-color: #FFA87D;
    border-color: #FFA87D; }
  .btn-outline-warning:focus, .btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5); }
  .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #FFA87D;
    background-color: transparent; }
  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
  .show > .btn-outline-warning.dropdown-toggle {
    color: #2A2E30;
    background-color: #FFA87D;
    border-color: #FFA87D; }
    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(255, 168, 125, 0.5); }

.btn-outline-danger {
  color: #FF7588;
  background-color: transparent;
  background-image: none;
  border-color: #FF7588; }
  .btn-outline-danger:hover {
    color: #2A2E30;
    background-color: #FF7588;
    border-color: #FF7588; }
  .btn-outline-danger:focus, .btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5); }
  .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #FF7588;
    background-color: transparent; }
  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
  .show > .btn-outline-danger.dropdown-toggle {
    color: #2A2E30;
    background-color: #FF7588;
    border-color: #FF7588; }
    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(255, 117, 136, 0.5); }

.btn-outline-light {
  color: #BABFC7;
  background-color: transparent;
  background-image: none;
  border-color: #BABFC7; }
  .btn-outline-light:hover {
    color: #2A2E30;
    background-color: #BABFC7;
    border-color: #BABFC7; }
  .btn-outline-light:focus, .btn-outline-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5); }
  .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #BABFC7;
    background-color: transparent; }
  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
  .show > .btn-outline-light.dropdown-toggle {
    color: #2A2E30;
    background-color: #BABFC7;
    border-color: #BABFC7; }
    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(186, 191, 199, 0.5); }

.btn-outline-dark {
  color: #1B2942;
  background-color: transparent;
  background-image: none;
  border-color: #1B2942; }
  .btn-outline-dark:hover {
    color: #fff;
    background-color: #1B2942;
    border-color: #1B2942; }
  .btn-outline-dark:focus, .btn-outline-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5); }
  .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #1B2942;
    background-color: transparent; }
  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
  .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #1B2942;
    border-color: #1B2942; }
    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5);
      box-shadow: 0 0 0 0.2rem rgba(27, 41, 66, 0.5); }

.btn-link {
  font-weight: 400;
  color: #009c9f;
  background-color: transparent; }
  .btn-link:hover {
    color: #008385;
    text-decoration: none;
    background-color: transparent;
    border-color: transparent; }
  .btn-link:focus, .btn-link.focus {
    text-decoration: none;
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn-link:disabled, .btn-link.disabled {
    color: #404E67;
    pointer-events: none; }

.btn-lg {
  padding: 1rem 1.25rem;
  font-size: 1.25rem;
  border-radius: 0.27rem; }

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.21rem; }

.btn-block {
  display: block;
  width: 100%; }
  .btn-block + .btn-block {
    margin-top: 0.5rem; }

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%; }

.fc-unthemed .fc-content, .fc-unthemed .fc-divider, .fc-unthemed .fc-popover, .fc-unthemed .fc-row, .fc-unthemed tbody, .fc-unthemed td, .fc-unthemed th, .fc-unthemed thead {
  border-color: #00ced2; }

th.fc-widget-header, .fc-basic-view td.fc-day-number, .fc-basic-view td.fc-week-number span {
  padding: 10px; }

.fc-state-default {
  background-image: none;
  -webkit-box-shadow: none;
  box-shadow: none; }

.fc button {
  height: auto;
  outline: none;
  border-radius: 0;
  text-shadow: none; }

.btn:focus, .fc button:focus, .fc button:focus, .btn.focus, .fc button.focus, .fc button.focus, .btn:active:focus, .fc button:active:focus, .fc button:active:focus, .btn:active.focus, .fc button:active.focus, .fc button:active.focus, .btn.active:focus, .fc button.active:focus, .fc button.active:focus, .btn.active.focus, .fc button.active.focus, .fc button.active.focus {
  outline: none; }

.fc-day-grid-event {
  margin: 0 5px 5px 5px;
  padding: 4px 8px; }

.fc-event {
  background: #00B5B8;
  border: 1px solid #009c9f; }
  .fc-event span {
    font-size: 1rem;
    color: #FFF; }

.fc-time-grid-event .fc-title {
  color: #FFF; }

.fc-unthemed .fc-today {
  color: #FFF;
  background: #00B5B8; }

.fc-unthemed .fc-divider, .fc-unthemed .fc-popover .fc-header {
  background: #E4EBF1; }

.fc-popover .fc-header {
  padding: 10px 5px; }

/* Styling for each event from Schedule */
.fc-time-grid-event.fc-v-event.fc-event {
  border-radius: 4px;
  border: none;
  padding: 5px;
  opacity: .65;
  left: 5% !important;
  right: 5% !important; }

/* Bolds the name of the event and inherits the font size */
.fc-event {
  font-size: inherit !important;
  font-weight: bold !important; }

/* Inherits background for each event from Schedule. */
.fc-event .fc-bg {
  z-index: 1 !important;
  background: inherit !important;
  opacity: .25 !important; }

/* Normal font weight for the time in each event */
.fc-time-grid-event .fc-time {
  font-weight: normal !important; }

/* Apply same opacity to all day events */
.fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start {
  opacity: .65 !important;
  margin-left: 12px !important;
  padding: 5px !important; }

/* Apply same opacity to all day events */
.fc-day-grid-event.fc-h-event.fc-event.fc-not-start.fc-end {
  opacity: .65 !important;
  margin-left: 12px !important;
  padding: 5px !important; }

.fc-time-grid .fc-slats td {
  height: auto; }

.fc-ltr .fc-axis {
  padding: 10px; }

.fc-nonbusiness {
  background: #D7E0EA; }

.fc-events-container {
  padding: 0 10px;
  border: 1px solid #A2B8CD;
  background: #F5F7FA;
  text-align: left; }

.fc-events-container .fc-event {
  padding: 5px;
  margin: 10px 0; }

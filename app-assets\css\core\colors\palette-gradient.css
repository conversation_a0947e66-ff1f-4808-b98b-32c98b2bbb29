.bg-gradient-x-white .card-header, .bg-gradient-x-white .card-footer, .bg-gradient-y-white .card-header, .bg-gradient-y-white .card-footer, .bg-gradient-directional-white .card-header, .bg-gradient-directional-white .card-footer, .bg-gradient-radial-white .card-header, .bg-gradient-radial-white .card-footer, .bg-gradient-striped-white .card-header, .bg-gradient-striped-white .card-footer, .bg-gradient-x2-white .card-header, .bg-gradient-x2-white .card-footer, .bg-gradient-y2-white .card-header, .bg-gradient-y2-white .card-footer {
  background-color: transparent; }

.bg-gradient-x-black .card-header, .bg-gradient-x-black .card-footer, .bg-gradient-y-black .card-header, .bg-gradient-y-black .card-footer, .bg-gradient-directional-black .card-header, .bg-gradient-directional-black .card-footer, .bg-gradient-radial-black .card-header, .bg-gradient-radial-black .card-footer, .bg-gradient-striped-black .card-header, .bg-gradient-striped-black .card-footer, .bg-gradient-x2-black .card-header, .bg-gradient-x2-black .card-footer, .bg-gradient-y2-black .card-header, .bg-gradient-y2-black .card-footer {
  background-color: transparent; }

.bg-gradient-x-primary {
  background-image: -webkit-gradient(linear, left top, right top, from(#00A5A8), to(#4DCBCD));
  background-image: -webkit-linear-gradient(left, #00A5A8 0%, #4DCBCD 100%);
  background-image: -moz-linear-gradient(left, #00A5A8 0%, #4DCBCD 100%);
  background-image: -o-linear-gradient(left, #00A5A8 0%, #4DCBCD 100%);
  background-image: linear-gradient(to right, #00A5A8 0%, #4DCBCD 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-primary {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#00A5A8), to(#4DCBCD));
  background-image: -webkit-linear-gradient(top, #00A5A8 0%, #4DCBCD 100%);
  background-image: -moz-linear-gradient(top, #00A5A8 0%, #4DCBCD 100%);
  background-image: -o-linear-gradient(top, #00A5A8 0%, #4DCBCD 100%);
  background-image: linear-gradient(to bottom, #00A5A8 0%, #4DCBCD 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-primary {
  background-image: -webkit-linear-gradient(45deg, #00A5A8, #4DCBCD);
  background-image: -moz-linear-gradient(45deg, #00A5A8, #4DCBCD);
  background-image: -o-linear-gradient(45deg, #00A5A8, #4DCBCD);
  background-image: linear-gradient(45deg, #00A5A8, #4DCBCD);
  background-repeat: repeat-x; }

.bg-gradient-x2-primary {
  background-image: -webkit-gradient(linear, left top, right top, from(#4DCBCD), color-stop(50%, #00B5B8), to(#80DADC));
  background-image: -webkit-linear-gradient(left, #4DCBCD, #00B5B8 50%, #80DADC);
  background-image: -moz-linear-gradient(left, #4DCBCD, #00B5B8 50%, #80DADC);
  background-image: -o-linear-gradient(left, #4DCBCD, #00B5B8 50%, #80DADC);
  background-image: linear-gradient(to right, #4DCBCD, #00B5B8 50%, #80DADC);
  background-repeat: no-repeat; }

.bg-gradient-y2-primary {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#4DCBCD), color-stop(50%, #00B5B8), to(#80DADC));
  background-image: -webkit-linear-gradient(#4DCBCD, #00B5B8 50%, #80DADC);
  background-image: -moz-linear-gradient(#4DCBCD, #00B5B8 50%, #80DADC);
  background-image: -o-linear-gradient(#4DCBCD, #00B5B8 50%, #80DADC);
  background-image: linear-gradient(#4DCBCD, #00B5B8 50%, #80DADC);
  background-repeat: no-repeat; }

.bg-gradient-radial-primary {
  background-image: -webkit-radial-gradient(circle, #00A5A8, #4DCBCD);
  background-image: -moz-radial-gradient(circle, #00A5A8, #4DCBCD);
  background-image: -o-radial-gradient(circle, #00A5A8, #4DCBCD);
  background-image: radial-gradient(circle, #00A5A8, #4DCBCD);
  background-repeat: no-repeat; }

.bg-gradient-striped-primary {
  background-image: -webkit-linear-gradient(45deg, #80DADC 25%, transparent 25%, transparent 50%, #80DADC 50%, #80DADC 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #80DADC 25%, transparent 25%, transparent 50%, #80DADC 50%, #80DADC 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #80DADC 25%, transparent 25%, transparent 50%, #80DADC 50%, #80DADC 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #80DADC 25%, transparent 25%, transparent 50%, #80DADC 50%, #80DADC 75%, transparent 75%, transparent); }

.bg-gradient-x-primary .card-header, .bg-gradient-x-primary .card-footer, .bg-gradient-y-primary .card-header, .bg-gradient-y-primary .card-footer, .bg-gradient-directional-primary .card-header, .bg-gradient-directional-primary .card-footer, .bg-gradient-radial-primary .card-header, .bg-gradient-radial-primary .card-footer, .bg-gradient-striped-primary .card-header, .bg-gradient-striped-primary .card-footer, .bg-gradient-x2-primary .card-header, .bg-gradient-x2-primary .card-footer, .bg-gradient-y2-primary .card-header, .bg-gradient-y2-primary .card-footer {
  background-color: transparent; }

.bg-gradient-x-success {
  background-image: -webkit-gradient(linear, left top, right top, from(#10C888), to(#5CE0B8));
  background-image: -webkit-linear-gradient(left, #10C888 0%, #5CE0B8 100%);
  background-image: -moz-linear-gradient(left, #10C888 0%, #5CE0B8 100%);
  background-image: -o-linear-gradient(left, #10C888 0%, #5CE0B8 100%);
  background-image: linear-gradient(to right, #10C888 0%, #5CE0B8 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-success {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#10C888), to(#5CE0B8));
  background-image: -webkit-linear-gradient(top, #10C888 0%, #5CE0B8 100%);
  background-image: -moz-linear-gradient(top, #10C888 0%, #5CE0B8 100%);
  background-image: -o-linear-gradient(top, #10C888 0%, #5CE0B8 100%);
  background-image: linear-gradient(to bottom, #10C888 0%, #5CE0B8 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-success {
  background-image: -webkit-linear-gradient(45deg, #10C888, #5CE0B8);
  background-image: -moz-linear-gradient(45deg, #10C888, #5CE0B8);
  background-image: -o-linear-gradient(45deg, #10C888, #5CE0B8);
  background-image: linear-gradient(45deg, #10C888, #5CE0B8);
  background-repeat: repeat-x; }

.bg-gradient-x2-success {
  background-image: -webkit-gradient(linear, left top, right top, from(#5CE0B8), color-stop(50%, #16D39A), to(#8BE9CD));
  background-image: -webkit-linear-gradient(left, #5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: -moz-linear-gradient(left, #5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: -o-linear-gradient(left, #5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: linear-gradient(to right, #5CE0B8, #16D39A 50%, #8BE9CD);
  background-repeat: no-repeat; }

.bg-gradient-y2-success {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#5CE0B8), color-stop(50%, #16D39A), to(#8BE9CD));
  background-image: -webkit-linear-gradient(#5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: -moz-linear-gradient(#5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: -o-linear-gradient(#5CE0B8, #16D39A 50%, #8BE9CD);
  background-image: linear-gradient(#5CE0B8, #16D39A 50%, #8BE9CD);
  background-repeat: no-repeat; }

.bg-gradient-radial-success {
  background-image: -webkit-radial-gradient(circle, #10C888, #5CE0B8);
  background-image: -moz-radial-gradient(circle, #10C888, #5CE0B8);
  background-image: -o-radial-gradient(circle, #10C888, #5CE0B8);
  background-image: radial-gradient(circle, #10C888, #5CE0B8);
  background-repeat: no-repeat; }

.bg-gradient-striped-success {
  background-image: -webkit-linear-gradient(45deg, #8BE9CD 25%, transparent 25%, transparent 50%, #8BE9CD 50%, #8BE9CD 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #8BE9CD 25%, transparent 25%, transparent 50%, #8BE9CD 50%, #8BE9CD 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #8BE9CD 25%, transparent 25%, transparent 50%, #8BE9CD 50%, #8BE9CD 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #8BE9CD 25%, transparent 25%, transparent 50%, #8BE9CD 50%, #8BE9CD 75%, transparent 75%, transparent); }

.bg-gradient-x-success .card-header, .bg-gradient-x-success .card-footer, .bg-gradient-y-success .card-header, .bg-gradient-y-success .card-footer, .bg-gradient-directional-success .card-header, .bg-gradient-directional-success .card-footer, .bg-gradient-radial-success .card-header, .bg-gradient-radial-success .card-footer, .bg-gradient-striped-success .card-header, .bg-gradient-striped-success .card-footer, .bg-gradient-x2-success .card-header, .bg-gradient-x2-success .card-footer, .bg-gradient-y2-success .card-header, .bg-gradient-y2-success .card-footer {
  background-color: transparent; }

.bg-gradient-x-info {
  background-image: -webkit-gradient(linear, left top, right top, from(#22C2DC), to(#6CDDEB));
  background-image: -webkit-linear-gradient(left, #22C2DC 0%, #6CDDEB 100%);
  background-image: -moz-linear-gradient(left, #22C2DC 0%, #6CDDEB 100%);
  background-image: -o-linear-gradient(left, #22C2DC 0%, #6CDDEB 100%);
  background-image: linear-gradient(to right, #22C2DC 0%, #6CDDEB 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-info {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#22C2DC), to(#6CDDEB));
  background-image: -webkit-linear-gradient(top, #22C2DC 0%, #6CDDEB 100%);
  background-image: -moz-linear-gradient(top, #22C2DC 0%, #6CDDEB 100%);
  background-image: -o-linear-gradient(top, #22C2DC 0%, #6CDDEB 100%);
  background-image: linear-gradient(to bottom, #22C2DC 0%, #6CDDEB 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-info {
  background-image: -webkit-linear-gradient(45deg, #22C2DC, #6CDDEB);
  background-image: -moz-linear-gradient(45deg, #22C2DC, #6CDDEB);
  background-image: -o-linear-gradient(45deg, #22C2DC, #6CDDEB);
  background-image: linear-gradient(45deg, #22C2DC, #6CDDEB);
  background-repeat: repeat-x; }

.bg-gradient-x2-info {
  background-image: -webkit-gradient(linear, left top, right top, from(#6CDDEB), color-stop(50%, #2DCEE3), to(#96E7F1));
  background-image: -webkit-linear-gradient(left, #6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: -moz-linear-gradient(left, #6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: -o-linear-gradient(left, #6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: linear-gradient(to right, #6CDDEB, #2DCEE3 50%, #96E7F1);
  background-repeat: no-repeat; }

.bg-gradient-y2-info {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#6CDDEB), color-stop(50%, #2DCEE3), to(#96E7F1));
  background-image: -webkit-linear-gradient(#6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: -moz-linear-gradient(#6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: -o-linear-gradient(#6CDDEB, #2DCEE3 50%, #96E7F1);
  background-image: linear-gradient(#6CDDEB, #2DCEE3 50%, #96E7F1);
  background-repeat: no-repeat; }

.bg-gradient-radial-info {
  background-image: -webkit-radial-gradient(circle, #22C2DC, #6CDDEB);
  background-image: -moz-radial-gradient(circle, #22C2DC, #6CDDEB);
  background-image: -o-radial-gradient(circle, #22C2DC, #6CDDEB);
  background-image: radial-gradient(circle, #22C2DC, #6CDDEB);
  background-repeat: no-repeat; }

.bg-gradient-striped-info {
  background-image: -webkit-linear-gradient(45deg, #96E7F1 25%, transparent 25%, transparent 50%, #96E7F1 50%, #96E7F1 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #96E7F1 25%, transparent 25%, transparent 50%, #96E7F1 50%, #96E7F1 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #96E7F1 25%, transparent 25%, transparent 50%, #96E7F1 50%, #96E7F1 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #96E7F1 25%, transparent 25%, transparent 50%, #96E7F1 50%, #96E7F1 75%, transparent 75%, transparent); }

.bg-gradient-x-info .card-header, .bg-gradient-x-info .card-footer, .bg-gradient-y-info .card-header, .bg-gradient-y-info .card-footer, .bg-gradient-directional-info .card-header, .bg-gradient-directional-info .card-footer, .bg-gradient-radial-info .card-header, .bg-gradient-radial-info .card-footer, .bg-gradient-striped-info .card-header, .bg-gradient-striped-info .card-footer, .bg-gradient-x2-info .card-header, .bg-gradient-x2-info .card-footer, .bg-gradient-y2-info .card-header, .bg-gradient-y2-info .card-footer {
  background-color: transparent; }

.bg-gradient-x-warning {
  background-image: -webkit-gradient(linear, left top, right top, from(#FF976A), to(#FFC2A4));
  background-image: -webkit-linear-gradient(left, #FF976A 0%, #FFC2A4 100%);
  background-image: -moz-linear-gradient(left, #FF976A 0%, #FFC2A4 100%);
  background-image: -o-linear-gradient(left, #FF976A 0%, #FFC2A4 100%);
  background-image: linear-gradient(to right, #FF976A 0%, #FFC2A4 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-warning {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FF976A), to(#FFC2A4));
  background-image: -webkit-linear-gradient(top, #FF976A 0%, #FFC2A4 100%);
  background-image: -moz-linear-gradient(top, #FF976A 0%, #FFC2A4 100%);
  background-image: -o-linear-gradient(top, #FF976A 0%, #FFC2A4 100%);
  background-image: linear-gradient(to bottom, #FF976A 0%, #FFC2A4 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-warning {
  background-image: -webkit-linear-gradient(45deg, #FF976A, #FFC2A4);
  background-image: -moz-linear-gradient(45deg, #FF976A, #FFC2A4);
  background-image: -o-linear-gradient(45deg, #FF976A, #FFC2A4);
  background-image: linear-gradient(45deg, #FF976A, #FFC2A4);
  background-repeat: repeat-x; }

.bg-gradient-x2-warning {
  background-image: -webkit-gradient(linear, left top, right top, from(#FFC2A4), color-stop(50%, #FFA87D), to(#FFD4BE));
  background-image: -webkit-linear-gradient(left, #FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: -moz-linear-gradient(left, #FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: -o-linear-gradient(left, #FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: linear-gradient(to right, #FFC2A4, #FFA87D 50%, #FFD4BE);
  background-repeat: no-repeat; }

.bg-gradient-y2-warning {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FFC2A4), color-stop(50%, #FFA87D), to(#FFD4BE));
  background-image: -webkit-linear-gradient(#FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: -moz-linear-gradient(#FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: -o-linear-gradient(#FFC2A4, #FFA87D 50%, #FFD4BE);
  background-image: linear-gradient(#FFC2A4, #FFA87D 50%, #FFD4BE);
  background-repeat: no-repeat; }

.bg-gradient-radial-warning {
  background-image: -webkit-radial-gradient(circle, #FF976A, #FFC2A4);
  background-image: -moz-radial-gradient(circle, #FF976A, #FFC2A4);
  background-image: -o-radial-gradient(circle, #FF976A, #FFC2A4);
  background-image: radial-gradient(circle, #FF976A, #FFC2A4);
  background-repeat: no-repeat; }

.bg-gradient-striped-warning {
  background-image: -webkit-linear-gradient(45deg, #FFD4BE 25%, transparent 25%, transparent 50%, #FFD4BE 50%, #FFD4BE 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #FFD4BE 25%, transparent 25%, transparent 50%, #FFD4BE 50%, #FFD4BE 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #FFD4BE 25%, transparent 25%, transparent 50%, #FFD4BE 50%, #FFD4BE 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #FFD4BE 25%, transparent 25%, transparent 50%, #FFD4BE 50%, #FFD4BE 75%, transparent 75%, transparent); }

.bg-gradient-x-warning .card-header, .bg-gradient-x-warning .card-footer, .bg-gradient-y-warning .card-header, .bg-gradient-y-warning .card-footer, .bg-gradient-directional-warning .card-header, .bg-gradient-directional-warning .card-footer, .bg-gradient-radial-warning .card-header, .bg-gradient-radial-warning .card-footer, .bg-gradient-striped-warning .card-header, .bg-gradient-striped-warning .card-footer, .bg-gradient-x2-warning .card-header, .bg-gradient-x2-warning .card-footer, .bg-gradient-y2-warning .card-header, .bg-gradient-y2-warning .card-footer {
  background-color: transparent; }

.bg-gradient-x-danger {
  background-image: -webkit-gradient(linear, left top, right top, from(#FF6275), to(#FF9EAC));
  background-image: -webkit-linear-gradient(left, #FF6275 0%, #FF9EAC 100%);
  background-image: -moz-linear-gradient(left, #FF6275 0%, #FF9EAC 100%);
  background-image: -o-linear-gradient(left, #FF6275 0%, #FF9EAC 100%);
  background-image: linear-gradient(to right, #FF6275 0%, #FF9EAC 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-danger {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FF6275), to(#FF9EAC));
  background-image: -webkit-linear-gradient(top, #FF6275 0%, #FF9EAC 100%);
  background-image: -moz-linear-gradient(top, #FF6275 0%, #FF9EAC 100%);
  background-image: -o-linear-gradient(top, #FF6275 0%, #FF9EAC 100%);
  background-image: linear-gradient(to bottom, #FF6275 0%, #FF9EAC 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-danger {
  background-image: -webkit-linear-gradient(45deg, #FF6275, #FF9EAC);
  background-image: -moz-linear-gradient(45deg, #FF6275, #FF9EAC);
  background-image: -o-linear-gradient(45deg, #FF6275, #FF9EAC);
  background-image: linear-gradient(45deg, #FF6275, #FF9EAC);
  background-repeat: repeat-x; }

.bg-gradient-x2-danger {
  background-image: -webkit-gradient(linear, left top, right top, from(#FF9EAC), color-stop(50%, #FF7588), to(#FFBAC4));
  background-image: -webkit-linear-gradient(left, #FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: -moz-linear-gradient(left, #FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: -o-linear-gradient(left, #FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: linear-gradient(to right, #FF9EAC, #FF7588 50%, #FFBAC4);
  background-repeat: no-repeat; }

.bg-gradient-y2-danger {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FF9EAC), color-stop(50%, #FF7588), to(#FFBAC4));
  background-image: -webkit-linear-gradient(#FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: -moz-linear-gradient(#FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: -o-linear-gradient(#FF9EAC, #FF7588 50%, #FFBAC4);
  background-image: linear-gradient(#FF9EAC, #FF7588 50%, #FFBAC4);
  background-repeat: no-repeat; }

.bg-gradient-radial-danger {
  background-image: -webkit-radial-gradient(circle, #FF6275, #FF9EAC);
  background-image: -moz-radial-gradient(circle, #FF6275, #FF9EAC);
  background-image: -o-radial-gradient(circle, #FF6275, #FF9EAC);
  background-image: radial-gradient(circle, #FF6275, #FF9EAC);
  background-repeat: no-repeat; }

.bg-gradient-striped-danger {
  background-image: -webkit-linear-gradient(45deg, #FFBAC4 25%, transparent 25%, transparent 50%, #FFBAC4 50%, #FFBAC4 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #FFBAC4 25%, transparent 25%, transparent 50%, #FFBAC4 50%, #FFBAC4 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #FFBAC4 25%, transparent 25%, transparent 50%, #FFBAC4 50%, #FFBAC4 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #FFBAC4 25%, transparent 25%, transparent 50%, #FFBAC4 50%, #FFBAC4 75%, transparent 75%, transparent); }

.bg-gradient-x-danger .card-header, .bg-gradient-x-danger .card-footer, .bg-gradient-y-danger .card-header, .bg-gradient-y-danger .card-footer, .bg-gradient-directional-danger .card-header, .bg-gradient-directional-danger .card-footer, .bg-gradient-radial-danger .card-header, .bg-gradient-radial-danger .card-footer, .bg-gradient-striped-danger .card-header, .bg-gradient-striped-danger .card-footer, .bg-gradient-x2-danger .card-header, .bg-gradient-x2-danger .card-footer, .bg-gradient-y2-danger .card-header, .bg-gradient-y2-danger .card-footer {
  background-color: transparent; }

.bg-gradient-x-red {
  background-image: -webkit-gradient(linear, left top, right top, from(#D32F2F), to(#E57373));
  background-image: -webkit-linear-gradient(left, #D32F2F 0%, #E57373 100%);
  background-image: -moz-linear-gradient(left, #D32F2F 0%, #E57373 100%);
  background-image: -o-linear-gradient(left, #D32F2F 0%, #E57373 100%);
  background-image: linear-gradient(to right, #D32F2F 0%, #E57373 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-red {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#D32F2F), to(#E57373));
  background-image: -webkit-linear-gradient(top, #D32F2F 0%, #E57373 100%);
  background-image: -moz-linear-gradient(top, #D32F2F 0%, #E57373 100%);
  background-image: -o-linear-gradient(top, #D32F2F 0%, #E57373 100%);
  background-image: linear-gradient(to bottom, #D32F2F 0%, #E57373 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-red {
  background-image: -webkit-linear-gradient(45deg, #D32F2F, #E57373);
  background-image: -moz-linear-gradient(45deg, #D32F2F, #E57373);
  background-image: -o-linear-gradient(45deg, #D32F2F, #E57373);
  background-image: linear-gradient(45deg, #D32F2F, #E57373);
  background-repeat: repeat-x; }

.bg-gradient-x2-red {
  background-image: -webkit-gradient(linear, left top, right top, from(#E57373), color-stop(50%, #F44336), to(#EF9A9A));
  background-image: -webkit-linear-gradient(left, #E57373, #F44336 50%, #EF9A9A);
  background-image: -moz-linear-gradient(left, #E57373, #F44336 50%, #EF9A9A);
  background-image: -o-linear-gradient(left, #E57373, #F44336 50%, #EF9A9A);
  background-image: linear-gradient(to right, #E57373, #F44336 50%, #EF9A9A);
  background-repeat: no-repeat; }

.bg-gradient-y2-red {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#E57373), color-stop(50%, #F44336), to(#EF9A9A));
  background-image: -webkit-linear-gradient(#E57373, #F44336 50%, #EF9A9A);
  background-image: -moz-linear-gradient(#E57373, #F44336 50%, #EF9A9A);
  background-image: -o-linear-gradient(#E57373, #F44336 50%, #EF9A9A);
  background-image: linear-gradient(#E57373, #F44336 50%, #EF9A9A);
  background-repeat: no-repeat; }

.bg-gradient-radial-red {
  background-image: -webkit-radial-gradient(circle, #D32F2F, #E57373);
  background-image: -moz-radial-gradient(circle, #D32F2F, #E57373);
  background-image: -o-radial-gradient(circle, #D32F2F, #E57373);
  background-image: radial-gradient(circle, #D32F2F, #E57373);
  background-repeat: no-repeat; }

.bg-gradient-striped-red {
  background-image: -webkit-linear-gradient(45deg, #EF9A9A 25%, transparent 25%, transparent 50%, #EF9A9A 50%, #EF9A9A 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #EF9A9A 25%, transparent 25%, transparent 50%, #EF9A9A 50%, #EF9A9A 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #EF9A9A 25%, transparent 25%, transparent 50%, #EF9A9A 50%, #EF9A9A 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #EF9A9A 25%, transparent 25%, transparent 50%, #EF9A9A 50%, #EF9A9A 75%, transparent 75%, transparent); }

.bg-gradient-x-red .card-header, .bg-gradient-x-red .card-footer, .bg-gradient-y-red .card-header, .bg-gradient-y-red .card-footer, .bg-gradient-directional-red .card-header, .bg-gradient-directional-red .card-footer, .bg-gradient-radial-red .card-header, .bg-gradient-radial-red .card-footer, .bg-gradient-striped-red .card-header, .bg-gradient-striped-red .card-footer, .bg-gradient-x2-red .card-header, .bg-gradient-x2-red .card-footer, .bg-gradient-y2-red .card-header, .bg-gradient-y2-red .card-footer {
  background-color: transparent; }

.bg-gradient-x-pink {
  background-image: -webkit-gradient(linear, left top, right top, from(#C2185B), to(#F06292));
  background-image: -webkit-linear-gradient(left, #C2185B 0%, #F06292 100%);
  background-image: -moz-linear-gradient(left, #C2185B 0%, #F06292 100%);
  background-image: -o-linear-gradient(left, #C2185B 0%, #F06292 100%);
  background-image: linear-gradient(to right, #C2185B 0%, #F06292 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-pink {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#C2185B), to(#F06292));
  background-image: -webkit-linear-gradient(top, #C2185B 0%, #F06292 100%);
  background-image: -moz-linear-gradient(top, #C2185B 0%, #F06292 100%);
  background-image: -o-linear-gradient(top, #C2185B 0%, #F06292 100%);
  background-image: linear-gradient(to bottom, #C2185B 0%, #F06292 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-pink {
  background-image: -webkit-linear-gradient(45deg, #C2185B, #F06292);
  background-image: -moz-linear-gradient(45deg, #C2185B, #F06292);
  background-image: -o-linear-gradient(45deg, #C2185B, #F06292);
  background-image: linear-gradient(45deg, #C2185B, #F06292);
  background-repeat: repeat-x; }

.bg-gradient-x2-pink {
  background-image: -webkit-gradient(linear, left top, right top, from(#F06292), color-stop(50%, #E91E63), to(#F48FB1));
  background-image: -webkit-linear-gradient(left, #F06292, #E91E63 50%, #F48FB1);
  background-image: -moz-linear-gradient(left, #F06292, #E91E63 50%, #F48FB1);
  background-image: -o-linear-gradient(left, #F06292, #E91E63 50%, #F48FB1);
  background-image: linear-gradient(to right, #F06292, #E91E63 50%, #F48FB1);
  background-repeat: no-repeat; }

.bg-gradient-y2-pink {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#F06292), color-stop(50%, #E91E63), to(#F48FB1));
  background-image: -webkit-linear-gradient(#F06292, #E91E63 50%, #F48FB1);
  background-image: -moz-linear-gradient(#F06292, #E91E63 50%, #F48FB1);
  background-image: -o-linear-gradient(#F06292, #E91E63 50%, #F48FB1);
  background-image: linear-gradient(#F06292, #E91E63 50%, #F48FB1);
  background-repeat: no-repeat; }

.bg-gradient-radial-pink {
  background-image: -webkit-radial-gradient(circle, #C2185B, #F06292);
  background-image: -moz-radial-gradient(circle, #C2185B, #F06292);
  background-image: -o-radial-gradient(circle, #C2185B, #F06292);
  background-image: radial-gradient(circle, #C2185B, #F06292);
  background-repeat: no-repeat; }

.bg-gradient-striped-pink {
  background-image: -webkit-linear-gradient(45deg, #F48FB1 25%, transparent 25%, transparent 50%, #F48FB1 50%, #F48FB1 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #F48FB1 25%, transparent 25%, transparent 50%, #F48FB1 50%, #F48FB1 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #F48FB1 25%, transparent 25%, transparent 50%, #F48FB1 50%, #F48FB1 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #F48FB1 25%, transparent 25%, transparent 50%, #F48FB1 50%, #F48FB1 75%, transparent 75%, transparent); }

.bg-gradient-x-pink .card-header, .bg-gradient-x-pink .card-footer, .bg-gradient-y-pink .card-header, .bg-gradient-y-pink .card-footer, .bg-gradient-directional-pink .card-header, .bg-gradient-directional-pink .card-footer, .bg-gradient-radial-pink .card-header, .bg-gradient-radial-pink .card-footer, .bg-gradient-striped-pink .card-header, .bg-gradient-striped-pink .card-footer, .bg-gradient-x2-pink .card-header, .bg-gradient-x2-pink .card-footer, .bg-gradient-y2-pink .card-header, .bg-gradient-y2-pink .card-footer {
  background-color: transparent; }

.bg-gradient-x-purple {
  background-image: -webkit-gradient(linear, left top, right top, from(#7B1FA2), to(#BA68C8));
  background-image: -webkit-linear-gradient(left, #7B1FA2 0%, #BA68C8 100%);
  background-image: -moz-linear-gradient(left, #7B1FA2 0%, #BA68C8 100%);
  background-image: -o-linear-gradient(left, #7B1FA2 0%, #BA68C8 100%);
  background-image: linear-gradient(to right, #7B1FA2 0%, #BA68C8 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-purple {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#7B1FA2), to(#BA68C8));
  background-image: -webkit-linear-gradient(top, #7B1FA2 0%, #BA68C8 100%);
  background-image: -moz-linear-gradient(top, #7B1FA2 0%, #BA68C8 100%);
  background-image: -o-linear-gradient(top, #7B1FA2 0%, #BA68C8 100%);
  background-image: linear-gradient(to bottom, #7B1FA2 0%, #BA68C8 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-purple {
  background-image: -webkit-linear-gradient(45deg, #7B1FA2, #BA68C8);
  background-image: -moz-linear-gradient(45deg, #7B1FA2, #BA68C8);
  background-image: -o-linear-gradient(45deg, #7B1FA2, #BA68C8);
  background-image: linear-gradient(45deg, #7B1FA2, #BA68C8);
  background-repeat: repeat-x; }

.bg-gradient-x2-purple {
  background-image: -webkit-gradient(linear, left top, right top, from(#BA68C8), color-stop(50%, #9C27B0), to(#CE93D8));
  background-image: -webkit-linear-gradient(left, #BA68C8, #9C27B0 50%, #CE93D8);
  background-image: -moz-linear-gradient(left, #BA68C8, #9C27B0 50%, #CE93D8);
  background-image: -o-linear-gradient(left, #BA68C8, #9C27B0 50%, #CE93D8);
  background-image: linear-gradient(to right, #BA68C8, #9C27B0 50%, #CE93D8);
  background-repeat: no-repeat; }

.bg-gradient-y2-purple {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#BA68C8), color-stop(50%, #9C27B0), to(#CE93D8));
  background-image: -webkit-linear-gradient(#BA68C8, #9C27B0 50%, #CE93D8);
  background-image: -moz-linear-gradient(#BA68C8, #9C27B0 50%, #CE93D8);
  background-image: -o-linear-gradient(#BA68C8, #9C27B0 50%, #CE93D8);
  background-image: linear-gradient(#BA68C8, #9C27B0 50%, #CE93D8);
  background-repeat: no-repeat; }

.bg-gradient-radial-purple {
  background-image: -webkit-radial-gradient(circle, #7B1FA2, #BA68C8);
  background-image: -moz-radial-gradient(circle, #7B1FA2, #BA68C8);
  background-image: -o-radial-gradient(circle, #7B1FA2, #BA68C8);
  background-image: radial-gradient(circle, #7B1FA2, #BA68C8);
  background-repeat: no-repeat; }

.bg-gradient-striped-purple {
  background-image: -webkit-linear-gradient(45deg, #CE93D8 25%, transparent 25%, transparent 50%, #CE93D8 50%, #CE93D8 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #CE93D8 25%, transparent 25%, transparent 50%, #CE93D8 50%, #CE93D8 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #CE93D8 25%, transparent 25%, transparent 50%, #CE93D8 50%, #CE93D8 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #CE93D8 25%, transparent 25%, transparent 50%, #CE93D8 50%, #CE93D8 75%, transparent 75%, transparent); }

.bg-gradient-x-purple .card-header, .bg-gradient-x-purple .card-footer, .bg-gradient-y-purple .card-header, .bg-gradient-y-purple .card-footer, .bg-gradient-directional-purple .card-header, .bg-gradient-directional-purple .card-footer, .bg-gradient-radial-purple .card-header, .bg-gradient-radial-purple .card-footer, .bg-gradient-striped-purple .card-header, .bg-gradient-striped-purple .card-footer, .bg-gradient-x2-purple .card-header, .bg-gradient-x2-purple .card-footer, .bg-gradient-y2-purple .card-header, .bg-gradient-y2-purple .card-footer {
  background-color: transparent; }

.bg-gradient-x-blue {
  background-image: -webkit-gradient(linear, left top, right top, from(#1976D2), to(#64B5F6));
  background-image: -webkit-linear-gradient(left, #1976D2 0%, #64B5F6 100%);
  background-image: -moz-linear-gradient(left, #1976D2 0%, #64B5F6 100%);
  background-image: -o-linear-gradient(left, #1976D2 0%, #64B5F6 100%);
  background-image: linear-gradient(to right, #1976D2 0%, #64B5F6 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-blue {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#1976D2), to(#64B5F6));
  background-image: -webkit-linear-gradient(top, #1976D2 0%, #64B5F6 100%);
  background-image: -moz-linear-gradient(top, #1976D2 0%, #64B5F6 100%);
  background-image: -o-linear-gradient(top, #1976D2 0%, #64B5F6 100%);
  background-image: linear-gradient(to bottom, #1976D2 0%, #64B5F6 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-blue {
  background-image: -webkit-linear-gradient(45deg, #1976D2, #64B5F6);
  background-image: -moz-linear-gradient(45deg, #1976D2, #64B5F6);
  background-image: -o-linear-gradient(45deg, #1976D2, #64B5F6);
  background-image: linear-gradient(45deg, #1976D2, #64B5F6);
  background-repeat: repeat-x; }

.bg-gradient-x2-blue {
  background-image: -webkit-gradient(linear, left top, right top, from(#64B5F6), color-stop(50%, #2196F3), to(#90CAF9));
  background-image: -webkit-linear-gradient(left, #64B5F6, #2196F3 50%, #90CAF9);
  background-image: -moz-linear-gradient(left, #64B5F6, #2196F3 50%, #90CAF9);
  background-image: -o-linear-gradient(left, #64B5F6, #2196F3 50%, #90CAF9);
  background-image: linear-gradient(to right, #64B5F6, #2196F3 50%, #90CAF9);
  background-repeat: no-repeat; }

.bg-gradient-y2-blue {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#64B5F6), color-stop(50%, #2196F3), to(#90CAF9));
  background-image: -webkit-linear-gradient(#64B5F6, #2196F3 50%, #90CAF9);
  background-image: -moz-linear-gradient(#64B5F6, #2196F3 50%, #90CAF9);
  background-image: -o-linear-gradient(#64B5F6, #2196F3 50%, #90CAF9);
  background-image: linear-gradient(#64B5F6, #2196F3 50%, #90CAF9);
  background-repeat: no-repeat; }

.bg-gradient-radial-blue {
  background-image: -webkit-radial-gradient(circle, #1976D2, #64B5F6);
  background-image: -moz-radial-gradient(circle, #1976D2, #64B5F6);
  background-image: -o-radial-gradient(circle, #1976D2, #64B5F6);
  background-image: radial-gradient(circle, #1976D2, #64B5F6);
  background-repeat: no-repeat; }

.bg-gradient-striped-blue {
  background-image: -webkit-linear-gradient(45deg, #90CAF9 25%, transparent 25%, transparent 50%, #90CAF9 50%, #90CAF9 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #90CAF9 25%, transparent 25%, transparent 50%, #90CAF9 50%, #90CAF9 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #90CAF9 25%, transparent 25%, transparent 50%, #90CAF9 50%, #90CAF9 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #90CAF9 25%, transparent 25%, transparent 50%, #90CAF9 50%, #90CAF9 75%, transparent 75%, transparent); }

.bg-gradient-x-blue .card-header, .bg-gradient-x-blue .card-footer, .bg-gradient-y-blue .card-header, .bg-gradient-y-blue .card-footer, .bg-gradient-directional-blue .card-header, .bg-gradient-directional-blue .card-footer, .bg-gradient-radial-blue .card-header, .bg-gradient-radial-blue .card-footer, .bg-gradient-striped-blue .card-header, .bg-gradient-striped-blue .card-footer, .bg-gradient-x2-blue .card-header, .bg-gradient-x2-blue .card-footer, .bg-gradient-y2-blue .card-header, .bg-gradient-y2-blue .card-footer {
  background-color: transparent; }

.bg-gradient-x-cyan {
  background-image: -webkit-gradient(linear, left top, right top, from(#0097A7), to(#4DD0E1));
  background-image: -webkit-linear-gradient(left, #0097A7 0%, #4DD0E1 100%);
  background-image: -moz-linear-gradient(left, #0097A7 0%, #4DD0E1 100%);
  background-image: -o-linear-gradient(left, #0097A7 0%, #4DD0E1 100%);
  background-image: linear-gradient(to right, #0097A7 0%, #4DD0E1 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-cyan {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#0097A7), to(#4DD0E1));
  background-image: -webkit-linear-gradient(top, #0097A7 0%, #4DD0E1 100%);
  background-image: -moz-linear-gradient(top, #0097A7 0%, #4DD0E1 100%);
  background-image: -o-linear-gradient(top, #0097A7 0%, #4DD0E1 100%);
  background-image: linear-gradient(to bottom, #0097A7 0%, #4DD0E1 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-cyan {
  background-image: -webkit-linear-gradient(45deg, #0097A7, #4DD0E1);
  background-image: -moz-linear-gradient(45deg, #0097A7, #4DD0E1);
  background-image: -o-linear-gradient(45deg, #0097A7, #4DD0E1);
  background-image: linear-gradient(45deg, #0097A7, #4DD0E1);
  background-repeat: repeat-x; }

.bg-gradient-x2-cyan {
  background-image: -webkit-gradient(linear, left top, right top, from(#4DD0E1), color-stop(50%, #00BCD4), to(#80DEEA));
  background-image: -webkit-linear-gradient(left, #4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: -moz-linear-gradient(left, #4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: -o-linear-gradient(left, #4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: linear-gradient(to right, #4DD0E1, #00BCD4 50%, #80DEEA);
  background-repeat: no-repeat; }

.bg-gradient-y2-cyan {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#4DD0E1), color-stop(50%, #00BCD4), to(#80DEEA));
  background-image: -webkit-linear-gradient(#4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: -moz-linear-gradient(#4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: -o-linear-gradient(#4DD0E1, #00BCD4 50%, #80DEEA);
  background-image: linear-gradient(#4DD0E1, #00BCD4 50%, #80DEEA);
  background-repeat: no-repeat; }

.bg-gradient-radial-cyan {
  background-image: -webkit-radial-gradient(circle, #0097A7, #4DD0E1);
  background-image: -moz-radial-gradient(circle, #0097A7, #4DD0E1);
  background-image: -o-radial-gradient(circle, #0097A7, #4DD0E1);
  background-image: radial-gradient(circle, #0097A7, #4DD0E1);
  background-repeat: no-repeat; }

.bg-gradient-striped-cyan {
  background-image: -webkit-linear-gradient(45deg, #80DEEA 25%, transparent 25%, transparent 50%, #80DEEA 50%, #80DEEA 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #80DEEA 25%, transparent 25%, transparent 50%, #80DEEA 50%, #80DEEA 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #80DEEA 25%, transparent 25%, transparent 50%, #80DEEA 50%, #80DEEA 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #80DEEA 25%, transparent 25%, transparent 50%, #80DEEA 50%, #80DEEA 75%, transparent 75%, transparent); }

.bg-gradient-x-cyan .card-header, .bg-gradient-x-cyan .card-footer, .bg-gradient-y-cyan .card-header, .bg-gradient-y-cyan .card-footer, .bg-gradient-directional-cyan .card-header, .bg-gradient-directional-cyan .card-footer, .bg-gradient-radial-cyan .card-header, .bg-gradient-radial-cyan .card-footer, .bg-gradient-striped-cyan .card-header, .bg-gradient-striped-cyan .card-footer, .bg-gradient-x2-cyan .card-header, .bg-gradient-x2-cyan .card-footer, .bg-gradient-y2-cyan .card-header, .bg-gradient-y2-cyan .card-footer {
  background-color: transparent; }

.bg-gradient-x-teal {
  background-image: -webkit-gradient(linear, left top, right top, from(#00796B), to(#4DB6AC));
  background-image: -webkit-linear-gradient(left, #00796B 0%, #4DB6AC 100%);
  background-image: -moz-linear-gradient(left, #00796B 0%, #4DB6AC 100%);
  background-image: -o-linear-gradient(left, #00796B 0%, #4DB6AC 100%);
  background-image: linear-gradient(to right, #00796B 0%, #4DB6AC 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-teal {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#00796B), to(#4DB6AC));
  background-image: -webkit-linear-gradient(top, #00796B 0%, #4DB6AC 100%);
  background-image: -moz-linear-gradient(top, #00796B 0%, #4DB6AC 100%);
  background-image: -o-linear-gradient(top, #00796B 0%, #4DB6AC 100%);
  background-image: linear-gradient(to bottom, #00796B 0%, #4DB6AC 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-teal {
  background-image: -webkit-linear-gradient(45deg, #00796B, #4DB6AC);
  background-image: -moz-linear-gradient(45deg, #00796B, #4DB6AC);
  background-image: -o-linear-gradient(45deg, #00796B, #4DB6AC);
  background-image: linear-gradient(45deg, #00796B, #4DB6AC);
  background-repeat: repeat-x; }

.bg-gradient-x2-teal {
  background-image: -webkit-gradient(linear, left top, right top, from(#4DB6AC), color-stop(50%, #009688), to(#80CBC4));
  background-image: -webkit-linear-gradient(left, #4DB6AC, #009688 50%, #80CBC4);
  background-image: -moz-linear-gradient(left, #4DB6AC, #009688 50%, #80CBC4);
  background-image: -o-linear-gradient(left, #4DB6AC, #009688 50%, #80CBC4);
  background-image: linear-gradient(to right, #4DB6AC, #009688 50%, #80CBC4);
  background-repeat: no-repeat; }

.bg-gradient-y2-teal {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#4DB6AC), color-stop(50%, #009688), to(#80CBC4));
  background-image: -webkit-linear-gradient(#4DB6AC, #009688 50%, #80CBC4);
  background-image: -moz-linear-gradient(#4DB6AC, #009688 50%, #80CBC4);
  background-image: -o-linear-gradient(#4DB6AC, #009688 50%, #80CBC4);
  background-image: linear-gradient(#4DB6AC, #009688 50%, #80CBC4);
  background-repeat: no-repeat; }

.bg-gradient-radial-teal {
  background-image: -webkit-radial-gradient(circle, #00796B, #4DB6AC);
  background-image: -moz-radial-gradient(circle, #00796B, #4DB6AC);
  background-image: -o-radial-gradient(circle, #00796B, #4DB6AC);
  background-image: radial-gradient(circle, #00796B, #4DB6AC);
  background-repeat: no-repeat; }

.bg-gradient-striped-teal {
  background-image: -webkit-linear-gradient(45deg, #80CBC4 25%, transparent 25%, transparent 50%, #80CBC4 50%, #80CBC4 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #80CBC4 25%, transparent 25%, transparent 50%, #80CBC4 50%, #80CBC4 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #80CBC4 25%, transparent 25%, transparent 50%, #80CBC4 50%, #80CBC4 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #80CBC4 25%, transparent 25%, transparent 50%, #80CBC4 50%, #80CBC4 75%, transparent 75%, transparent); }

.bg-gradient-x-teal .card-header, .bg-gradient-x-teal .card-footer, .bg-gradient-y-teal .card-header, .bg-gradient-y-teal .card-footer, .bg-gradient-directional-teal .card-header, .bg-gradient-directional-teal .card-footer, .bg-gradient-radial-teal .card-header, .bg-gradient-radial-teal .card-footer, .bg-gradient-striped-teal .card-header, .bg-gradient-striped-teal .card-footer, .bg-gradient-x2-teal .card-header, .bg-gradient-x2-teal .card-footer, .bg-gradient-y2-teal .card-header, .bg-gradient-y2-teal .card-footer {
  background-color: transparent; }

.bg-gradient-x-yellow {
  background-image: -webkit-gradient(linear, left top, right top, from(#FBC02D), to(#FFF176));
  background-image: -webkit-linear-gradient(left, #FBC02D 0%, #FFF176 100%);
  background-image: -moz-linear-gradient(left, #FBC02D 0%, #FFF176 100%);
  background-image: -o-linear-gradient(left, #FBC02D 0%, #FFF176 100%);
  background-image: linear-gradient(to right, #FBC02D 0%, #FFF176 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-yellow {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FBC02D), to(#FFF176));
  background-image: -webkit-linear-gradient(top, #FBC02D 0%, #FFF176 100%);
  background-image: -moz-linear-gradient(top, #FBC02D 0%, #FFF176 100%);
  background-image: -o-linear-gradient(top, #FBC02D 0%, #FFF176 100%);
  background-image: linear-gradient(to bottom, #FBC02D 0%, #FFF176 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-yellow {
  background-image: -webkit-linear-gradient(45deg, #FBC02D, #FFF176);
  background-image: -moz-linear-gradient(45deg, #FBC02D, #FFF176);
  background-image: -o-linear-gradient(45deg, #FBC02D, #FFF176);
  background-image: linear-gradient(45deg, #FBC02D, #FFF176);
  background-repeat: repeat-x; }

.bg-gradient-x2-yellow {
  background-image: -webkit-gradient(linear, left top, right top, from(#FFF176), color-stop(50%, #FFEB3B), to(#FFF59D));
  background-image: -webkit-linear-gradient(left, #FFF176, #FFEB3B 50%, #FFF59D);
  background-image: -moz-linear-gradient(left, #FFF176, #FFEB3B 50%, #FFF59D);
  background-image: -o-linear-gradient(left, #FFF176, #FFEB3B 50%, #FFF59D);
  background-image: linear-gradient(to right, #FFF176, #FFEB3B 50%, #FFF59D);
  background-repeat: no-repeat; }

.bg-gradient-y2-yellow {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FFF176), color-stop(50%, #FFEB3B), to(#FFF59D));
  background-image: -webkit-linear-gradient(#FFF176, #FFEB3B 50%, #FFF59D);
  background-image: -moz-linear-gradient(#FFF176, #FFEB3B 50%, #FFF59D);
  background-image: -o-linear-gradient(#FFF176, #FFEB3B 50%, #FFF59D);
  background-image: linear-gradient(#FFF176, #FFEB3B 50%, #FFF59D);
  background-repeat: no-repeat; }

.bg-gradient-radial-yellow {
  background-image: -webkit-radial-gradient(circle, #FBC02D, #FFF176);
  background-image: -moz-radial-gradient(circle, #FBC02D, #FFF176);
  background-image: -o-radial-gradient(circle, #FBC02D, #FFF176);
  background-image: radial-gradient(circle, #FBC02D, #FFF176);
  background-repeat: no-repeat; }

.bg-gradient-striped-yellow {
  background-image: -webkit-linear-gradient(45deg, #FFF59D 25%, transparent 25%, transparent 50%, #FFF59D 50%, #FFF59D 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #FFF59D 25%, transparent 25%, transparent 50%, #FFF59D 50%, #FFF59D 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #FFF59D 25%, transparent 25%, transparent 50%, #FFF59D 50%, #FFF59D 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #FFF59D 25%, transparent 25%, transparent 50%, #FFF59D 50%, #FFF59D 75%, transparent 75%, transparent); }

.bg-gradient-x-yellow .card-header, .bg-gradient-x-yellow .card-footer, .bg-gradient-y-yellow .card-header, .bg-gradient-y-yellow .card-footer, .bg-gradient-directional-yellow .card-header, .bg-gradient-directional-yellow .card-footer, .bg-gradient-radial-yellow .card-header, .bg-gradient-radial-yellow .card-footer, .bg-gradient-striped-yellow .card-header, .bg-gradient-striped-yellow .card-footer, .bg-gradient-x2-yellow .card-header, .bg-gradient-x2-yellow .card-footer, .bg-gradient-y2-yellow .card-header, .bg-gradient-y2-yellow .card-footer {
  background-color: transparent; }

.bg-gradient-x-amber {
  background-image: -webkit-gradient(linear, left top, right top, from(#FFA000), to(#FFD54F));
  background-image: -webkit-linear-gradient(left, #FFA000 0%, #FFD54F 100%);
  background-image: -moz-linear-gradient(left, #FFA000 0%, #FFD54F 100%);
  background-image: -o-linear-gradient(left, #FFA000 0%, #FFD54F 100%);
  background-image: linear-gradient(to right, #FFA000 0%, #FFD54F 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-amber {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FFA000), to(#FFD54F));
  background-image: -webkit-linear-gradient(top, #FFA000 0%, #FFD54F 100%);
  background-image: -moz-linear-gradient(top, #FFA000 0%, #FFD54F 100%);
  background-image: -o-linear-gradient(top, #FFA000 0%, #FFD54F 100%);
  background-image: linear-gradient(to bottom, #FFA000 0%, #FFD54F 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-amber {
  background-image: -webkit-linear-gradient(45deg, #FFA000, #FFD54F);
  background-image: -moz-linear-gradient(45deg, #FFA000, #FFD54F);
  background-image: -o-linear-gradient(45deg, #FFA000, #FFD54F);
  background-image: linear-gradient(45deg, #FFA000, #FFD54F);
  background-repeat: repeat-x; }

.bg-gradient-x2-amber {
  background-image: -webkit-gradient(linear, left top, right top, from(#FFD54F), color-stop(50%, #FFC107), to(#FFE082));
  background-image: -webkit-linear-gradient(left, #FFD54F, #FFC107 50%, #FFE082);
  background-image: -moz-linear-gradient(left, #FFD54F, #FFC107 50%, #FFE082);
  background-image: -o-linear-gradient(left, #FFD54F, #FFC107 50%, #FFE082);
  background-image: linear-gradient(to right, #FFD54F, #FFC107 50%, #FFE082);
  background-repeat: no-repeat; }

.bg-gradient-y2-amber {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#FFD54F), color-stop(50%, #FFC107), to(#FFE082));
  background-image: -webkit-linear-gradient(#FFD54F, #FFC107 50%, #FFE082);
  background-image: -moz-linear-gradient(#FFD54F, #FFC107 50%, #FFE082);
  background-image: -o-linear-gradient(#FFD54F, #FFC107 50%, #FFE082);
  background-image: linear-gradient(#FFD54F, #FFC107 50%, #FFE082);
  background-repeat: no-repeat; }

.bg-gradient-radial-amber {
  background-image: -webkit-radial-gradient(circle, #FFA000, #FFD54F);
  background-image: -moz-radial-gradient(circle, #FFA000, #FFD54F);
  background-image: -o-radial-gradient(circle, #FFA000, #FFD54F);
  background-image: radial-gradient(circle, #FFA000, #FFD54F);
  background-repeat: no-repeat; }

.bg-gradient-striped-amber {
  background-image: -webkit-linear-gradient(45deg, #FFE082 25%, transparent 25%, transparent 50%, #FFE082 50%, #FFE082 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #FFE082 25%, transparent 25%, transparent 50%, #FFE082 50%, #FFE082 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #FFE082 25%, transparent 25%, transparent 50%, #FFE082 50%, #FFE082 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #FFE082 25%, transparent 25%, transparent 50%, #FFE082 50%, #FFE082 75%, transparent 75%, transparent); }

.bg-gradient-x-amber .card-header, .bg-gradient-x-amber .card-footer, .bg-gradient-y-amber .card-header, .bg-gradient-y-amber .card-footer, .bg-gradient-directional-amber .card-header, .bg-gradient-directional-amber .card-footer, .bg-gradient-radial-amber .card-header, .bg-gradient-radial-amber .card-footer, .bg-gradient-striped-amber .card-header, .bg-gradient-striped-amber .card-footer, .bg-gradient-x2-amber .card-header, .bg-gradient-x2-amber .card-footer, .bg-gradient-y2-amber .card-header, .bg-gradient-y2-amber .card-footer {
  background-color: transparent; }

.bg-gradient-x-blue-grey {
  background-image: -webkit-gradient(linear, left top, right top, from(#455A64), to(#90A4AE));
  background-image: -webkit-linear-gradient(left, #455A64 0%, #90A4AE 100%);
  background-image: -moz-linear-gradient(left, #455A64 0%, #90A4AE 100%);
  background-image: -o-linear-gradient(left, #455A64 0%, #90A4AE 100%);
  background-image: linear-gradient(to right, #455A64 0%, #90A4AE 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-blue-grey {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#455A64), to(#90A4AE));
  background-image: -webkit-linear-gradient(top, #455A64 0%, #90A4AE 100%);
  background-image: -moz-linear-gradient(top, #455A64 0%, #90A4AE 100%);
  background-image: -o-linear-gradient(top, #455A64 0%, #90A4AE 100%);
  background-image: linear-gradient(to bottom, #455A64 0%, #90A4AE 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-blue-grey {
  background-image: -webkit-linear-gradient(45deg, #455A64, #90A4AE);
  background-image: -moz-linear-gradient(45deg, #455A64, #90A4AE);
  background-image: -o-linear-gradient(45deg, #455A64, #90A4AE);
  background-image: linear-gradient(45deg, #455A64, #90A4AE);
  background-repeat: repeat-x; }

.bg-gradient-x2-blue-grey {
  background-image: -webkit-gradient(linear, left top, right top, from(#90A4AE), color-stop(50%, #607D8B), to(#B0BEC5));
  background-image: -webkit-linear-gradient(left, #90A4AE, #607D8B 50%, #B0BEC5);
  background-image: -moz-linear-gradient(left, #90A4AE, #607D8B 50%, #B0BEC5);
  background-image: -o-linear-gradient(left, #90A4AE, #607D8B 50%, #B0BEC5);
  background-image: linear-gradient(to right, #90A4AE, #607D8B 50%, #B0BEC5);
  background-repeat: no-repeat; }

.bg-gradient-y2-blue-grey {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#90A4AE), color-stop(50%, #607D8B), to(#B0BEC5));
  background-image: -webkit-linear-gradient(#90A4AE, #607D8B 50%, #B0BEC5);
  background-image: -moz-linear-gradient(#90A4AE, #607D8B 50%, #B0BEC5);
  background-image: -o-linear-gradient(#90A4AE, #607D8B 50%, #B0BEC5);
  background-image: linear-gradient(#90A4AE, #607D8B 50%, #B0BEC5);
  background-repeat: no-repeat; }

.bg-gradient-radial-blue-grey {
  background-image: -webkit-radial-gradient(circle, #455A64, #90A4AE);
  background-image: -moz-radial-gradient(circle, #455A64, #90A4AE);
  background-image: -o-radial-gradient(circle, #455A64, #90A4AE);
  background-image: radial-gradient(circle, #455A64, #90A4AE);
  background-repeat: no-repeat; }

.bg-gradient-striped-blue-grey {
  background-image: -webkit-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent); }

.bg-gradient-x-blue-grey .card-header, .bg-gradient-x-blue-grey .card-footer, .bg-gradient-y-blue-grey .card-header, .bg-gradient-y-blue-grey .card-footer, .bg-gradient-directional-blue-grey .card-header, .bg-gradient-directional-blue-grey .card-footer, .bg-gradient-radial-blue-grey .card-header, .bg-gradient-radial-blue-grey .card-footer, .bg-gradient-striped-blue-grey .card-header, .bg-gradient-striped-blue-grey .card-footer, .bg-gradient-x2-blue-grey .card-header, .bg-gradient-x2-blue-grey .card-footer, .bg-gradient-y2-blue-grey .card-header, .bg-gradient-y2-blue-grey .card-footer {
  background-color: transparent; }

.bg-gradient-x-grey-blue {
  background-image: -webkit-gradient(linear, left top, right top, from(#404E67), to(#6F85AD));
  background-image: -webkit-linear-gradient(left, #404E67 0%, #6F85AD 100%);
  background-image: -moz-linear-gradient(left, #404E67 0%, #6F85AD 100%);
  background-image: -o-linear-gradient(left, #404E67 0%, #6F85AD 100%);
  background-image: linear-gradient(to right, #404E67 0%, #6F85AD 100%);
  background-repeat: repeat-x; }

.bg-gradient-y-grey-blue {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#404E67), to(#6F85AD));
  background-image: -webkit-linear-gradient(top, #404E67 0%, #6F85AD 100%);
  background-image: -moz-linear-gradient(top, #404E67 0%, #6F85AD 100%);
  background-image: -o-linear-gradient(top, #404E67 0%, #6F85AD 100%);
  background-image: linear-gradient(to bottom, #404E67 0%, #6F85AD 100%);
  background-repeat: repeat-x; }

.bg-gradient-directional-grey-blue {
  background-image: -webkit-linear-gradient(45deg, #404E67, #6F85AD);
  background-image: -moz-linear-gradient(45deg, #404E67, #6F85AD);
  background-image: -o-linear-gradient(45deg, #404E67, #6F85AD);
  background-image: linear-gradient(45deg, #404E67, #6F85AD);
  background-repeat: repeat-x; }

.bg-gradient-x2-grey-blue {
  background-image: -webkit-gradient(linear, left top, right top, from(#6F85AD), color-stop(50%, #1B2942), to(#B0BEC5));
  background-image: -webkit-linear-gradient(left, #6F85AD, #1B2942 50%, #B0BEC5);
  background-image: -moz-linear-gradient(left, #6F85AD, #1B2942 50%, #B0BEC5);
  background-image: -o-linear-gradient(left, #6F85AD, #1B2942 50%, #B0BEC5);
  background-image: linear-gradient(to right, #6F85AD, #1B2942 50%, #B0BEC5);
  background-repeat: no-repeat; }

.bg-gradient-y2-grey-blue {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#6F85AD), color-stop(50%, #1B2942), to(#B0BEC5));
  background-image: -webkit-linear-gradient(#6F85AD, #1B2942 50%, #B0BEC5);
  background-image: -moz-linear-gradient(#6F85AD, #1B2942 50%, #B0BEC5);
  background-image: -o-linear-gradient(#6F85AD, #1B2942 50%, #B0BEC5);
  background-image: linear-gradient(#6F85AD, #1B2942 50%, #B0BEC5);
  background-repeat: no-repeat; }

.bg-gradient-radial-grey-blue {
  background-image: -webkit-radial-gradient(circle, #404E67, #6F85AD);
  background-image: -moz-radial-gradient(circle, #404E67, #6F85AD);
  background-image: -o-radial-gradient(circle, #404E67, #6F85AD);
  background-image: radial-gradient(circle, #404E67, #6F85AD);
  background-repeat: no-repeat; }

.bg-gradient-striped-grey-blue {
  background-image: -webkit-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #B0BEC5 25%, transparent 25%, transparent 50%, #B0BEC5 50%, #B0BEC5 75%, transparent 75%, transparent); }

.bg-gradient-x-grey-blue .card-header, .bg-gradient-x-grey-blue .card-footer, .bg-gradient-y-grey-blue .card-header, .bg-gradient-y-grey-blue .card-footer, .bg-gradient-directional-grey-blue .card-header, .bg-gradient-directional-grey-blue .card-footer, .bg-gradient-radial-grey-blue .card-header, .bg-gradient-radial-grey-blue .card-footer, .bg-gradient-striped-grey-blue .card-header, .bg-gradient-striped-grey-blue .card-footer, .bg-gradient-x2-grey-blue .card-header, .bg-gradient-x2-grey-blue .card-footer, .bg-gradient-y2-grey-blue .card-header, .bg-gradient-y2-grey-blue .card-footer {
  background-color: transparent; }

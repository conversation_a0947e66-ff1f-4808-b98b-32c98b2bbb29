.main-menu .navigation li > a > i,
.main-menu .dropdown-menu i,
.main-menu .dropdown-user > a > i,
.main-menu .navigation > li > a > i
{
    float: right;
}

.dropdown-user.nav-item i
{
    float: right;
}

.horizontal-menu .main-menu-content #main-menu-navigation > li > a > i
{
    float: right;
}

.mega-dropdown-menu li a i,
.mega-dropdown-menu li h6 i
{
    float: right;
}

.menu-expanded .form-group i
{
    float: inherit;
}

.form-control-position i
{
    position: absolute;
    top: 25%;
    right: 25%;
}

.echart-container
{
    direction: ltr;
}

.mm-menu
{
    left: auto;
}

.dz-hidden-input
{
    position: relative !important;
}

.content-wrapper .sticky-wrapper,
.content-right-sidebar .sticky-wrapper
{
    float: left;
}

.ui-widget-icon-block
{
    right: 50%;
}

.ui-dialog .ui-dialog-titlebar-close
{
    right: 3rem;
}

.square.slider-xl .noUi-handle:before
{
    left: 5px;
}

.square.slider-xl .noUi-handle:after
{
    left: 10px;
}

.square .noUi-handle:before
{
    left: 2px;
}

.square .noUi-handle:after
{
    left: 7px;
}

.noUi-vertical.square .noUi-handle:before
{
    left: -1px;
}

.noUi-vertical.square .noUi-handle:after
{
    left: -1px;
}

#tubular-container
{
    z-index: 0 !important;
}

.btn i[class^='icon-'],
.btn i[class*=' icon-']
{
    line-height: 1.25rem;
}

code[class*='language-'],
pre[class*='language-']
{
    direction: ltr;
}

@media print
{
    code[class*='language-'],
    pre[class*='language-']
    {
        text-shadow: none;
    }
}

.jp-card .jp-card-front .jp-card-lower .jp-card-expiry
{
    right: 65%;
}

.email-application .app-content .list-group .list-group-item i
{
    float: right !important;
}

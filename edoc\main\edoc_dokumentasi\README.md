# Dokumentasi Kegiatan - E-Doc RSK Dharmais

## Deskripsi
Menu "Dokumentasi Kegiatan" adalah fitur untuk mengelola dokumentasi kegiatan dalam bentuk foto dan video yang dilakukan oleh unit-unit di RSK Dharmais.

## Fitur Utama

### 1. Form Input Dokumentasi
- **Ju<PERSON><PERSON>**: Input teks untuk nama kegiatan
- **<PERSON>gori <PERSON>kumentasi**: Dropdown dari tabel `edoc_refrensi` dengan JENIS = 1
- **Tanggal Kegiatan**: Input date picker
- **Lokasi Kegiatan**: Input teks untuk tempat kegiatan
- **Unit Penyelenggara**: 
  - Admin: Dropdown pilihan unit
  - User Unit: Otomatis sesuai unit login
- **<PERSON><PERSON>mentasi**: Dropdown dari `edoc_refrensi` dengan JENIS = 2
- **Upload Foto**: Multiple file upload untuk gambar
- **Input Link Video**: Multiple URL input untuk video
- **Deskripsi**: Textarea untuk keterangan tambahan

### 2. Penyimpanan File
- **Folder**: `edoc/main/file_dokumentasi/JUDUL_TANGGAL/`
- **Foto**: Disimpan sebagai file fisik dengan nama aman
- **Video**: Hanya URL yang disimpan di database
- **Database**: Detail file tersimpan di `edoc_dokumentasi_detail`

### 3. Validasi
- Minimal harus ada satu foto atau video
- Validasi tipe file gambar (JPEG, PNG, GIF)
- Maksimal ukuran file 5MB per foto
- Validasi URL untuk video

### 4. Tampilan Data
- DataTables dengan server-side processing
- Kolom: No, Tanggal, Judul, Kategori, Lokasi, Unit, Jenis Dokumentasi, Aksi
- Tombol aksi: Detail, Edit, Hapus

### 5. Modal Detail
- Informasi lengkap dokumentasi
- Tabel file dengan preview dan download
- Preview gambar dalam modal
- Link video membuka tab baru

### 6. Fitur Edit
- Form edit data utama
- Tambah file baru (foto/video)
- Hapus file existing (soft delete)
- Update informasi dokumentasi

### 7. Fitur Watermark
- Download gambar dengan watermark logo (transparansi 20%)
- Download original hanya untuk admin
- Logo diambil dari `assets/images/logo.png`

### 8. Hak Akses
- **Admin (hak_akses = 1)**:
  - Melihat semua data
  - Pilih unit penyelenggara
  - Download original tanpa watermark
  - CREATED_BY = '90901'
- **User Unit (hak_akses = 2)**:
  - Hanya data unit sendiri
  - Unit otomatis sesuai login
  - Download dengan watermark
  - CREATED_BY = username

## Struktur Database

### Tabel `edoc_dokumentasi`
```sql
ID (PK), JUDUL, ID_KATEGORI, TANGGAL, LOKASI, UNIT, 
ID_JENIS_DOK, KETERANGAN, FILE_PATH, CREATED_BY, 
CREATED_AT, STATUS
```

### Tabel `edoc_dokumentasi_detail`
```sql
ID (PK), ID_DOKUMENTASI (FK), FILE_PATH, TIPE_FILE, 
LOKASI_FILE, UPLOADED_AT, STATUS
```

### Tabel `edoc_refrensi`
```sql
ID (PK), JENIS, DESKRIPSI, STATUS, CREATED_AT
```
- JENIS = 1: Kategori Dokumentasi
- JENIS = 2: Jenis Dokumentasi

## Struktur Folder
```
edoc/main/edoc_dokumentasi/
├── index.php                 # Halaman utama
├── js/
│   └── dokumentasi.js        # JavaScript handler
├── ajax/
│   ├── dokumentasi_serverside.php
│   ├── get_detail_dokumentasi.php
│   └── get_dokumentasi.php
├── process/
│   ├── simpan_dokumentasi.php
│   ├── update_dokumentasi.php
│   ├── hapus_dokumentasi.php
│   ├── hapus_file.php
│   └── unduh_gambar.php
├── create_tables.sql         # SQL pembuatan tabel
├── install_tables.php       # Installer tabel
└── README.md                # Dokumentasi ini
```

## Instalasi

1. **Buat Tabel Database**
   ```
   Akses: http://localhost/surat/edoc/main/edoc_dokumentasi/install_tables.php
   ```

2. **Buat Folder Upload**
   ```
   mkdir edoc/main/file_dokumentasi/
   chmod 755 edoc/main/file_dokumentasi/
   ```

3. **Pastikan Logo Ada**
   ```
   File: edoc/assets/images/logo.png
   ```

4. **Update Menu Sidebar**
   Menu sudah ditambahkan ke `edoc/main/index.php`

## Penggunaan

1. **Login** ke sistem E-Doc
2. **Pilih menu** "Dokumentasi Kegiatan" di sidebar
3. **Klik tombol** "Tambah Dokumentasi"
4. **Isi form** dengan data kegiatan
5. **Upload foto** dan/atau **input URL video**
6. **Simpan** dokumentasi

## Teknologi
- **Backend**: PHP, MySQL
- **Frontend**: HTML, CSS, JavaScript, jQuery
- **UI Framework**: Bootstrap 3
- **DataTables**: Server-side processing
- **Notifications**: SweetAlert2, Toastr
- **Image Processing**: GD Library (untuk watermark)

## Catatan Penting
- Pastikan ekstensi GD PHP aktif untuk fitur watermark
- Folder upload harus memiliki permission write
- Backup database sebelum instalasi
- Test semua fitur setelah instalasi

## Support
Untuk pertanyaan atau masalah, hubungi developer sistem E-Doc RSK Dharmais.

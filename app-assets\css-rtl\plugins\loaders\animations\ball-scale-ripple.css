@-webkit-keyframes ball-scale-ripple
{
    0%
    {
        -webkit-transform: scale(.1);
                transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@-moz-keyframes ball-scale-ripple
{
    0%
    {
        -moz-transform: scale(.1);
             transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -moz-transform: scale(1);
             transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@-o-keyframes ball-scale-ripple
{
    0%
    {
        -o-transform: scale(.1);
           transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -o-transform: scale(1);
           transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@keyframes ball-scale-ripple
{
    0%
    {
        -webkit-transform: scale(.1);
           -moz-transform: scale(.1);
             -o-transform: scale(.1);
                transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

.ball-scale-ripple > div
{
    width: 50px;
    height: 50px;

    -webkit-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(.21, .53, .56, .8);
       -moz-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(.21, .53, .56, .8);
         -o-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(.21, .53, .56, .8);
            animation: ball-scale-ripple 1s 0s infinite cubic-bezier(.21, .53, .56, .8); 

    border: 2px solid #404e67;
    border-radius: 100%;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

@-webkit-keyframes ball-scale-multiple {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0; }
  5% {
    opacity: 1; }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0; } }

@-moz-keyframes ball-scale-multiple {
  0% {
    -moz-transform: scale(0);
    transform: scale(0);
    opacity: 0; }
  5% {
    opacity: 1; }
  100% {
    -moz-transform: scale(1);
    transform: scale(1);
    opacity: 0; } }

@-o-keyframes ball-scale-multiple {
  0% {
    -o-transform: scale(0);
    transform: scale(0);
    opacity: 0; }
  5% {
    opacity: 1; }
  100% {
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0; } }

@keyframes ball-scale-multiple {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    opacity: 0; }
  5% {
    opacity: 1; }
  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 0; } }

.ball-scale-multiple {
  position: relative; }
  .ball-scale-multiple > div:nth-child(2) {
    -webkit-animation-delay: -0.4s;
    -moz-animation-delay: -0.4s;
    -o-animation-delay: -0.4s;
    animation-delay: -0.4s; }
  .ball-scale-multiple > div:nth-child(3) {
    -webkit-animation-delay: -0.2s;
    -moz-animation-delay: -0.2s;
    -o-animation-delay: -0.2s;
    animation-delay: -0.2s; }
  .ball-scale-multiple > div {
    background-color: #404E67;
    width: 15px;
    height: 15px;
    border-radius: 100%;
    margin: 2px;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    top: 0px;
    opacity: 0;
    margin: 0;
    width: 60px;
    height: 60px;
    -webkit-animation: ball-scale-multiple 1s 0s linear infinite;
    -moz-animation: ball-scale-multiple 1s 0s linear infinite;
    -o-animation: ball-scale-multiple 1s 0s linear infinite;
    animation: ball-scale-multiple 1s 0s linear infinite; }

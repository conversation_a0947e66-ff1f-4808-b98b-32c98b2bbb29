@-webkit-keyframes ball-scale-ripple-multiple
{
    0%
    {
        -webkit-transform: scale(.1);
                transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -webkit-transform: scale(1);
                transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@-moz-keyframes ball-scale-ripple-multiple
{
    0%
    {
        -moz-transform: scale(.1);
             transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -moz-transform: scale(1);
             transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@-o-keyframes ball-scale-ripple-multiple
{
    0%
    {
        -o-transform: scale(.1);
           transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -o-transform: scale(1);
           transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

@keyframes ball-scale-ripple-multiple
{
    0%
    {
        -webkit-transform: scale(.1);
           -moz-transform: scale(.1);
             -o-transform: scale(.1);
                transform: scale(.1);

        opacity: 1;
    }
    70%
    {
        -webkit-transform: scale(1);
           -moz-transform: scale(1);
             -o-transform: scale(1);
                transform: scale(1);

        opacity: .7;
    }
    100%
    {
        opacity: .0;
    }
}

.ball-scale-ripple-multiple
{
    position: relative;

    -webkit-transform: translateY(-25px);
       -moz-transform: translateY(-25px);
        -ms-transform: translateY(-25px);
         -o-transform: translateY(-25px);
            transform: translateY(-25px);
}
.ball-scale-ripple-multiple > div:nth-child(0)
{
    -webkit-animation-delay: -.8s;
       -moz-animation-delay: -.8s;
         -o-animation-delay: -.8s;
            animation-delay: -.8s;
}
.ball-scale-ripple-multiple > div:nth-child(1)
{
    -webkit-animation-delay: -.6s;
       -moz-animation-delay: -.6s;
         -o-animation-delay: -.6s;
            animation-delay: -.6s;
}
.ball-scale-ripple-multiple > div:nth-child(2)
{
    -webkit-animation-delay: -.4s;
       -moz-animation-delay: -.4s;
         -o-animation-delay: -.4s;
            animation-delay: -.4s;
}
.ball-scale-ripple-multiple > div:nth-child(3)
{
    -webkit-animation-delay: -.2s;
       -moz-animation-delay: -.2s;
         -o-animation-delay: -.2s;
            animation-delay: -.2s;
}
.ball-scale-ripple-multiple > div
{
    position: absolute;
    top: -2px;
    right: -26px;

    width: 50px;
    height: 50px;

    -webkit-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8);
       -moz-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8);
         -o-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8);
            animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8); 

    border: 2px solid #404e67;
    border-radius: 100%;

    -webkit-animation-fill-mode: both;
       -moz-animation-fill-mode: both;
         -o-animation-fill-mode: both;
            animation-fill-mode: both;
}

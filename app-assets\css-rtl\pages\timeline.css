/* --------------------------------
Horizontal Timeline
-------------------------------- */
@-webkit-keyframes 'cd-enter-right'
{
    0%
    {
        -webkit-transform: translateX(-100%); 

        opacity: 0;
    }
    100%
    {
        -webkit-transform: translateX(0%); 

        opacity: 1;
    }
}

@-moz-keyframes 'cd-enter-right'
{
    0%
    {
        -moz-transform: translateX(-100%); 

        opacity: 0;
    }
    100%
    {
        -moz-transform: translateX(0%); 

        opacity: 1;
    }
}

@-o-keyframes 'cd-enter-right'
{
    0%
    {
        -o-transform: translateX(-100%);
           transform: translateX(-100%); 

        opacity: 0;
    }
    100%
    {
        -o-transform: translateX(0%);
           transform: translateX(0%); 

        opacity: 1;
    }
}

@keyframes 'cd-enter-right'
{
    0%
    {
        -webkit-transform: translateX(-100%);
           -moz-transform: translateX(-100%);
             -o-transform: translateX(-100%);
                transform: translateX(-100%); 

        opacity: 0;
    }
    100%
    {
        -webkit-transform: translateX(0%);
           -moz-transform: translateX(0%);
             -o-transform: translateX(0%);
                transform: translateX(0%); 

        opacity: 1;
    }
}

@-webkit-keyframes 'cd-enter-left'
{
    0%
    {
        -webkit-transform: translateX(100%); 

        opacity: 0;
    }
    100%
    {
        -webkit-transform: translateX(0%); 

        opacity: 1;
    }
}

@-moz-keyframes 'cd-enter-left'
{
    0%
    {
        -moz-transform: translateX(100%); 

        opacity: 0;
    }
    100%
    {
        -moz-transform: translateX(0%); 

        opacity: 1;
    }
}

@-o-keyframes 'cd-enter-left'
{
    0%
    {
        -o-transform: translateX(100%);
           transform: translateX(100%); 

        opacity: 0;
    }
    100%
    {
        -o-transform: translateX(0%);
           transform: translateX(0%); 

        opacity: 1;
    }
}

@keyframes 'cd-enter-left'
{
    0%
    {
        -webkit-transform: translateX(100%);
           -moz-transform: translateX(100%);
             -o-transform: translateX(100%);
                transform: translateX(100%); 

        opacity: 0;
    }
    100%
    {
        -webkit-transform: translateX(0%);
           -moz-transform: translateX(0%);
             -o-transform: translateX(0%);
                transform: translateX(0%); 

        opacity: 1;
    }
}

.cd-horizontal-timeline
{
    margin: 2em auto;

    -webkit-transition: opacity .2s;
       -moz-transition: opacity .2s;
         -o-transition: opacity .2s;
            transition: opacity .2s; 

    opacity: 0;
}
.cd-horizontal-timeline ol,
.cd-horizontal-timeline ul
{
    list-style: none;
}
.cd-horizontal-timeline::before
{
    display: none; 

    content: 'mobile';
}
.cd-horizontal-timeline .timeline
{
    position: relative;

    width: 90%;
    max-width: 800px;
    height: 100px;
    margin: 0 auto;
}
.cd-horizontal-timeline .events-wrapper
{
    position: relative;

    overflow: hidden; 

    height: 100%;
    margin: 0 40px;
}
.cd-horizontal-timeline .events-wrapper::after
{
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0; 

    width: 20px;
    height: 100%;

    content: '';
}
.cd-horizontal-timeline .events-wrapper::before
{
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0; 

    width: 20px;
    height: 100%;

    content: '';
}
.cd-horizontal-timeline .events
{
    position: absolute;
    z-index: 1;
    top: 39px;
    right: 0;

    height: 2px;

    -webkit-transition: -webkit-transform .4s;
       -moz-transition:         transform .4s, -moz-transform .4s;
         -o-transition:      -o-transform .4s;
            transition: -webkit-transform .4s;
            transition:         transform .4s;
            transition:         transform .4s, -webkit-transform .4s, -moz-transform .4s, -o-transform .4s; 

    background: #dfdfdf;
}
.cd-horizontal-timeline .events a
{
    font-size: 1.3rem;

    position: absolute;
    z-index: 2;
    bottom: 0;

    padding-bottom: 15px;

    -webkit-transform: translateZ(0);
       -moz-transform: translateZ(0);
            transform: translateZ(0); 
    text-align: center;

    color: #383838;
}
.cd-horizontal-timeline .events a::after
{
    position: absolute;
    right: 50%;
    bottom: -5px;
    left: auto;

    width: 12px;
    height: 12px;

    content: '';
    -webkit-transition: background-color .3s, border-color .3s;
       -moz-transition: background-color .3s, border-color .3s;
         -o-transition: background-color .3s, border-color .3s;
            transition: background-color .3s, border-color .3s; 
    -webkit-transform: translateX(50%);
       -moz-transform: translateX(50%);
        -ms-transform: translateX(50%);
         -o-transform: translateX(50%);
            transform: translateX(50%);

    border: 2px solid #dfdfdf;
    border-radius: 50%;
    background-color: #f8f8f8;
}
.cd-horizontal-timeline .events a.selected
{
    pointer-events: none;
}
.cd-horizontal-timeline .events a.selected::after
{
    border-color: #00b5b8; 
    background-color: #00b5b8;
}
.cd-horizontal-timeline .events a.older-event::after
{
    border-color: #00b5b8;
}
.cd-horizontal-timeline .filling-line
{
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;

    width: 100%;
    height: 100%;

    -webkit-transition: -webkit-transform .3s;
       -moz-transition:         transform .3s, -moz-transform .3s;
         -o-transition:      -o-transform .3s;
            transition: -webkit-transform .3s;
            transition:         transform .3s;
            transition:         transform .3s, -webkit-transform .3s, -moz-transform .3s, -o-transform .3s; 
    -webkit-transform: scaleX(0);
       -moz-transform: scaleX(0);
        -ms-transform: scaleX(0);
         -o-transform: scaleX(0);
            transform: scaleX(0);
    -webkit-transform-origin: right center;
       -moz-transform-origin: right center;
        -ms-transform-origin: right center;
         -o-transform-origin: right center;
            transform-origin: right center;

    background-color: #00b5b8;
}
.cd-horizontal-timeline .events-content
{
    position: relative;

    overflow: hidden;

    width: 100%;
    margin: 2em 0;

    -webkit-transition: height .4s;
       -moz-transition: height .4s;
         -o-transition: height .4s;
            transition: height .4s;
}
.cd-horizontal-timeline .events-content li
{
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;

    width: 100%;
    padding: 0 5%;

    -webkit-transform: translateX(100%);
       -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
         -o-transform: translateX(100%);
            transform: translateX(100%);
    -webkit-animation-duration: .4s;
       -moz-animation-duration: .4s;
         -o-animation-duration: .4s;
            animation-duration: .4s;
    -webkit-animation-timing-function: ease-in-out;
       -moz-animation-timing-function: ease-in-out;
         -o-animation-timing-function: ease-in-out;
            animation-timing-function: ease-in-out; 

    opacity: 0;
}
.cd-horizontal-timeline .events-content li > *
{
    max-width: 800px;
    margin: 0 auto;
}
.cd-horizontal-timeline .events-content li.selected
{
    position: relative;
    z-index: 2;

    -webkit-transform: translateX(0);
       -moz-transform: translateX(0);
        -ms-transform: translateX(0);
         -o-transform: translateX(0);
            transform: translateX(0); 

    opacity: 1;
}
.cd-horizontal-timeline .events-content li.enter-right
{
    -webkit-animation-name: cd-enter-right;
       -moz-animation-name: cd-enter-right;
         -o-animation-name: cd-enter-right;
            animation-name: cd-enter-right;
}
.cd-horizontal-timeline .events-content li.leave-right
{
    -webkit-animation-name: cd-enter-right;
       -moz-animation-name: cd-enter-right;
         -o-animation-name: cd-enter-right;
            animation-name: cd-enter-right;
    animation-direction: reverse;
}
.cd-horizontal-timeline .events-content li.enter-left
{
    -webkit-animation-name: cd-enter-left;
       -moz-animation-name: cd-enter-left;
         -o-animation-name: cd-enter-left;
            animation-name: cd-enter-left;
}
.cd-horizontal-timeline .events-content li.leave-left
{
    -webkit-animation-name: cd-enter-left;
       -moz-animation-name: cd-enter-left;
         -o-animation-name: cd-enter-left;
            animation-name: cd-enter-left;
    animation-direction: reverse;
}

.cd-horizontal-timeline.loaded
{
    opacity: 1;
}

.no-touch .cd-horizontal-timeline .events a:hover::after
{
    border-color: #00b5b8; 
    background-color: #00b5b8;
}

.no-touch .cd-timeline-navigation a:hover
{
    border-color: #00b5b8;
}

.no-touch .cd-timeline-navigation a.inactive:hover
{
    border-color: #dfdfdf;
}

.cd-timeline-navigation a
{
    position: absolute;
    z-index: 1;
    top: 50%;
    bottom: auto;

    overflow: hidden;

    width: 34px;
    height: 34px;

    -webkit-transition: border-color .3s;
       -moz-transition: border-color .3s;
         -o-transition: border-color .3s;
            transition: border-color .3s; 
    -webkit-transform: translateY(-50%);
       -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
         -o-transform: translateY(-50%);
            transform: translateY(-50%);
    white-space: nowrap;
    text-indent: 100%;

    color: transparent;
    border: 2px solid #dfdfdf;
    border-radius: 50%;
}
.cd-timeline-navigation a::after
{
    position: absolute;
    top: 50%;
    right: 50%;
    bottom: auto;
    left: auto;

    width: 16px;
    height: 16px;

    content: '';
    -webkit-transform: translateX(50%) translateY(-50%);
       -moz-transform: translateX(50%) translateY(-50%);
        -ms-transform: translateX(50%) translateY(-50%);
         -o-transform: translateX(50%) translateY(-50%);
            transform: translateX(50%) translateY(-50%);

    background: url(../../images/svg/cd-arrow.svg) no-repeat 100% 0;
}

.cd-timeline-navigation a.prev
{
    right: 0;

    -webkit-transform: translateY(-50%) rotate(-180deg);
       -moz-transform: translateY(-50%) rotate(-180deg);
        -ms-transform: translateY(-50%) rotate(-180deg);
         -o-transform: translateY(-50%) rotate(-180deg);
            transform: translateY(-50%) rotate(-180deg);
}

.cd-timeline-navigation a.next
{
    left: 0;
}

.cd-timeline-navigation a.inactive
{
    cursor: not-allowed;
}
.cd-timeline-navigation a.inactive::after
{
    background-position: 100% -16px;
}

@media only screen and (min-width: 1100px)
{
    .cd-horizontal-timeline::before
    {
        content: 'desktop';
    }
}

@media only screen and (min-width: 768px)
{
    .cd-horizontal-timeline .events-content h2
    {
        font-size: 4rem;
        font-weight: 800;
    }
    .cd-horizontal-timeline .events-content em
    {
        font-size: 1.5rem;
    }
}

/* Timeline */
.timeline
{
    position: relative;

    display: block;

    margin: 0;
    padding: 10px 0;

    list-style: none;
}
.timeline:after
{
    display: table;
    clear: both; 

    content: ' ';
}
.timeline:before
{
    display: table; 

    content: ' ';
}
.timeline > li
{
    list-style: none;
}
.timeline > li:nth-child(even)
{
    float: right;
    clear: right;
}
.timeline > li:nth-child(odd)
{
    float: left;
    clear: left;
}
.timeline .media
{
    font-size: 13px; 

    border-bottom: 1px solid #eee;
}
.timeline .media p
{
    font-size: 13px;
}
.timeline .media:last-child
{
    border-bottom: 0;
}

.timeline-line + .timeline-item
{
    margin-top: -20px;
}

.timeline-group
{
    position: relative;
    z-index: 1; 

    display: block;
    float: none !important;

    margin: 20px 0;

    text-align: center;
}

.timeline-poster
{
    margin-top: -20px;
}
.timeline-poster .btn-link
{
    color: #a1aab0;
}
.timeline-poster .btn-link:active
{
    color: #3e5771;
}
.timeline-poster .btn-link:focus
{
    color: #3e5771;
}
.timeline-poster .btn-link:hover
{
    color: #3e5771;
}
.timeline-poster .btn-link.active
{
    color: #3e5771;
}

#timeline.timeline-center .timeline-line
{
    position: absolute;
    top: 0;
    right: 50%;
    bottom: 0;

    display: inline-block;

    width: 2px;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0); 

    background-color: #ddd;
}

#timeline.timeline-center .timeline-item
{
    position: relative;

    display: inline-block;

    width: 50%;
    padding: 0 50px 35px;
}
#timeline.timeline-center .timeline-item:nth-child(even)
{
    padding: 0 0 50px 35px;
}
#timeline.timeline-center .timeline-item:nth-child(even):after
{
    position: absolute;
    z-index: 1;
    top: 10px;
    top: 20px;
    left: 19px;
    left: 25px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-right: 12px solid #ccc;
    border-right: 11px solid #fff; 
    border-bottom: 12px solid transparent;
}
#timeline.timeline-center .timeline-item:nth-child(even):before
{
    position: absolute;
    z-index: 1; 
    top: 20px;
    left: 24px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-right: 12px solid #ccc;
    border-bottom: 12px solid transparent;
}
#timeline.timeline-center .timeline-item:nth-child(even) > .timeline-badge
{
    left: -20px;
}
#timeline.timeline-center .timeline-item:nth-child(odd)
{
    padding: 0 35px 50px 0;
}
#timeline.timeline-center .timeline-item:nth-child(odd):after
{
    position: absolute;
    z-index: 1;
    top: 10px;
    top: 20px;
    right: 19px;
    right: 25px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-left: 12px solid #ccc;
    border-left: 11px solid #fff;
}
#timeline.timeline-center .timeline-item:nth-child(odd):before
{
    position: absolute;
    z-index: 1; 
    top: 20px;
    right: 24px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-left: 12px solid #ccc;
}
#timeline.timeline-center .timeline-item:nth-child(odd) > .timeline-badge
{
    right: -20px;
}
#timeline.timeline-center .timeline-item > .timeline-badge
{
    position: absolute;
    z-index: 1; 
    top: 12px;
}
#timeline.timeline-center .timeline-item > .timeline-badge > span
{
    display: inline-block;

    width: 40px;
    height: 40px;

    -webkit-transition: all ease .3s;
       -moz-transition: all ease .3s;
         -o-transition: all ease .3s;
            transition: all ease .3s; 
    text-align: center;
    text-decoration: none;

    border-radius: 50%;
}
#timeline.timeline-center .timeline-item > .timeline-badge > span i
{
    font-size: 1.6rem;
    line-height: 40px;

    top: 0; 

    display: block;

    color: #fff;
}
#timeline.timeline-center .timeline-item > .timeline-badge > span:active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-center .timeline-item > .timeline-badge > span:focus
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-center .timeline-item > .timeline-badge > span:hover
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-center .timeline-item > .timeline-badge > a.active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-center .timeline-item > .timeline-panel
{
    position: relative;

    border: 1px solid #ddd;
    border-radius: 4px; 
    background-color: #fff;
}
#timeline.timeline-center .timeline-item > .timeline-panel:hover .timeline-actions
{
    display: block;
}

#timeline.timeline-right .timeline-line
{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 12px;

    display: inline-block;

    width: 2px;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0); 

    background-color: #ddd;
}

#timeline.timeline-right .timeline-group
{
    text-align: left;
}

#timeline.timeline-right .timeline-item
{
    position: relative;

    display: inline-block;

    width: 100%;
    padding: 0 0 35px 43px;
}
#timeline.timeline-right .timeline-item:after
{
    position: absolute;
    z-index: 1;
    top: 21px;
    left: 33px;

    width: 0;
    height: 0;

    content: '';

    border-top: 11px solid #f5f7fa;
    border-right: 11px solid #fff; 
    border-bottom: 11px solid #f5f7fa;
}
#timeline.timeline-right .timeline-item:before
{
    position: absolute;
    z-index: 1; 
    top: 20px;
    left: 32px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-right: 12px solid #ccc;
    border-bottom: 12px solid transparent;
}
#timeline.timeline-right .timeline-item > .timeline-badge
{
    left: -8px;
}
#timeline.timeline-right .timeline-item > .timeline-badge
{
    position: absolute;
    z-index: 1; 
    top: 12px;
}
#timeline.timeline-right .timeline-item > .timeline-badge > span
{
    display: inline-block;

    width: 40px;
    height: 40px;

    -webkit-transition: all ease .3s;
       -moz-transition: all ease .3s;
         -o-transition: all ease .3s;
            transition: all ease .3s; 
    text-align: center;
    text-decoration: none;

    border-radius: 50%;
}
#timeline.timeline-right .timeline-item > .timeline-badge > span i
{
    font-size: 1.6rem;
    line-height: 40px;

    top: 0; 

    display: block;

    color: #fff;
}
#timeline.timeline-right .timeline-item > .timeline-badge > span:active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-right .timeline-item > .timeline-badge > span:focus
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-right .timeline-item > .timeline-badge > span:hover
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-right .timeline-item > .timeline-badge > a.active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-right .timeline-item > .timeline-panel
{
    position: relative;

    border: 1px solid #ddd;
    border-radius: 4px; 
    background-color: #fff;
}
#timeline.timeline-right .timeline-item > .timeline-panel:hover .timeline-actions
{
    display: block;
}

#timeline.timeline-right .timeline-item.block:nth-child(even):before
{
    right: auto; 
    left: 8px;
}

#timeline.timeline-right .timeline-item.block:nth-child(even):after
{
    right: auto; 
    left: 11px;
}

#timeline.timeline-right .timeline-item.block:nth-child(odd):after
{
    left: 8px;
    left: auto;
}

#timeline.timeline-left .timeline-line
{
    position: absolute;
    top: 0;
    right: 12px;
    bottom: 0;

    display: inline-block;

    width: 2px;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0); 

    background-color: #ddd;
}

#timeline.timeline-left .timeline-group
{
    text-align: right;
}

#timeline.timeline-left .timeline-item
{
    position: relative;

    display: inline-block;

    width: 100%;
    padding: 0 43px 35px 0;
}
#timeline.timeline-left .timeline-item:after
{
    position: absolute;
    z-index: 1;
    top: 21px;
    right: 33px;

    width: 0;
    height: 0;

    content: '';

    border-top: 11px solid #f5f7fa;
    border-bottom: 11px solid #f5f7fa;
    border-left: 11px solid #fff;
}
#timeline.timeline-left .timeline-item:before
{
    position: absolute;
    z-index: 1; 
    top: 20px;
    right: 32px;

    width: 0;
    height: 0;

    content: '';

    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-left: 12px solid #ccc;
}
#timeline.timeline-left .timeline-item > .timeline-badge
{
    right: -8px;
}
#timeline.timeline-left .timeline-item > .timeline-badge
{
    position: absolute;
    z-index: 1; 
    top: 12px;
}
#timeline.timeline-left .timeline-item > .timeline-badge > span
{
    display: inline-block;

    width: 40px;
    height: 40px;

    -webkit-transition: all ease .3s;
       -moz-transition: all ease .3s;
         -o-transition: all ease .3s;
            transition: all ease .3s; 
    text-align: center;
    text-decoration: none;

    border-radius: 50%;
}
#timeline.timeline-left .timeline-item > .timeline-badge > span i
{
    font-size: 1.6rem;
    line-height: 40px;

    top: 0; 

    display: block;

    color: #fff;
}
#timeline.timeline-left .timeline-item > .timeline-badge > span:active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-left .timeline-item > .timeline-badge > span:focus
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-left .timeline-item > .timeline-badge > span:hover
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-left .timeline-item > .timeline-badge > a.active
{
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}
#timeline.timeline-left .timeline-item > .timeline-panel
{
    position: relative;

    border: 1px solid #ddd;
    border-radius: 4px; 
    background-color: #fff;
}
#timeline.timeline-left .timeline-item > .timeline-panel:hover .timeline-actions
{
    display: block;
}

#timeline .timeline-item.block:nth-child(even)
{
    width: 100% !important;
    margin-top: 5px;
}
#timeline .timeline-item.block:nth-child(even):after
{
    top: -11px;
    top: -10px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0);

    border: 0;
    border: 0;
    border-right: 12px solid transparent;
    border-right: 11px solid #f5f7fa;
    border-bottom: 12px solid #ccc;
    border-bottom: 11px solid #fff; 
    border-left: 12px solid transparent;
    border-left: 11px solid #f5f7fa;
}
#timeline .timeline-item.block:nth-child(even):before
{
    top: -11px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0); 

    border: 0;
    border-right: 12px solid transparent;
    border-bottom: 12px solid #ccc;
    border-left: 12px solid transparent;
}
#timeline .timeline-item.block:nth-child(even) > .timeline-badge
{
    top: -28px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0);
}

#timeline .timeline-item.block:nth-child(odd)
{
    width: 100% !important;
    margin-top: 5px;
}
#timeline .timeline-item.block:nth-child(odd):after
{
    top: -11px;
    top: -10px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0);

    border: 0;
    border: 0;
    border-right: 12px solid transparent;
    border-right: 11px solid #f5f7fa;
    border-bottom: 12px solid #ccc;
    border-bottom: 11px solid #fff; 
    border-left: 12px solid transparent;
    border-left: 11px solid #f5f7fa;
}
#timeline .timeline-item.block:nth-child(odd):before
{
    top: -11px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0); 

    border: 0;
    border-right: 12px solid transparent;
    border-bottom: 12px solid #ccc;
    border-left: 12px solid transparent;
}
#timeline .timeline-item.block:nth-child(odd) > .timeline-badge
{
    top: -28px;
    right: 50%;
    left: auto;

    -webkit-transform: translate(50%, 0);
       -moz-transform: translate(50%, 0);
        -ms-transform: translate(50%, 0);
         -o-transform: translate(50%, 0);
            transform: translate(50%, 0);
}

.timeline-actions
{
    display: none;
}

.timeline-content
{
    padding: 15px;
}

.timeline-footer
{
    padding: 15px;

    border-top: 1px solid #eee;
    border-radius: 0 0 5px 5px; 
    background-color: #fbfcfc;
}

.timeline-heading
{
    font-size: 20px; 

    padding: 15px;
}

.timeline-liveliness
{
    padding: 15px;

    border-top: 1px solid #eee;
    background-color: #fbfcfc;
}

.timeline-heading + .timeline-content
{
    padding-top: 0;
}

.timeline-date
{
    font-size: 14px;

    color: #aaa;
}

.timeline-embed
{
    position: relative;

    height: 0; 
    padding-top: 25px;
    padding-bottom: 56.25%;
}
.timeline-embed .embed-element
{
    position: absolute;
    top: 0;
    right: 0;

    width: 100%;
    height: 100%;
}
.timeline-embed embed
{
    position: absolute;
    top: 0;
    right: 0;

    width: 100%;
    height: 100%;
}
.timeline-embed iframe
{
    position: absolute;
    top: 0;
    right: 0;

    width: 100%;
    height: 100%;
}
.timeline-embed object
{
    position: absolute;
    top: 0;
    right: 0;

    width: 100%;
    height: 100%;
}

.timeline-img
{
    display: block;

    padding: 5px 0;
}

.timeline-img.first
{
    margin-left: -10px;
}

.timeline-img.middle
{
    margin-right: -10px; 
    margin-left: -10px;
}

.timeline-img.last
{
    margin-right: -10px;
}

.timeline-resume
{
    border-top: 1px solid #eee;
    background-color: #fbfcfc;
}

.timeline-avatar
{
    margin-top: -2px;
    margin-left: 10px;
}

.timeline-title
{
    font-size: 14px;
    font-weight: 600;
    line-height: 1.42857;

    margin: 0;
    padding: 0;

    text-decoration: none; 

    color: #3e5771;
}
.timeline-title > small
{
    font-size: 12px;
    line-height: 1.5;

    display: block;
    overflow: hidden;

    white-space: nowrap; 
    text-overflow: ellipsis;

    color: #a1aab0;
}

.wrapkit-content-rtl .timeline-avatar
{
    margin-right: 10px; 
    margin-left: 0;
}

@media (min-width: 992px)
{
    .timeline-item:nth-child(even)
    {
        padding: 0 0 50px 35px;
    }
    .timeline-item:nth-child(odd)
    {
        padding: 0 35px 50px 0;
    }
}

@media (max-width: 991.98px)
{
    .timeline
    {
        padding-right: 15px;
    }
    .timeline-line
    {
        right: 15px;
    }
    .timeline-group
    {
        display: inline-block;

        margin-right: -22px;
    }
    .timeline-item
    {
        width: 100% !important;
        margin-top: 0 !important;
        padding-left: 10px;
    }
    .timeline-item:nth-child(even):after
    {
        top: 10px;
        top: 21px;
        right: 19px;
        right: 40px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);

        border: 0;
        border-top: 12px solid transparent;
        border-top: 11px solid #f5f7fa;
        border-bottom: 12px solid transparent;
        border-bottom: 11px solid #f5f7fa;
        border-left: 12px solid #ccc;
        border-left: 11px solid #fff;
    }
    .timeline-item:nth-child(even):before
    {
        top: 20px;
        right: 39px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0); 

        border: 0;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-left: 12px solid #ccc;
    }
    .timeline-item:nth-child(even) > .timeline-badge
    {
        top: 12px;
        right: -20px;
        left: auto;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);
    }
    .timeline-item:nth-child(odd):after
    {
        top: 10px;
        top: 21px;
        right: 19px;
        right: 40px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);

        border: 0;
        border-top: 12px solid transparent;
        border-top: 11px solid #f5f7fa;
        border-bottom: 12px solid transparent;
        border-bottom: 11px solid #f5f7fa;
        border-left: 12px solid #ccc;
        border-left: 11px solid #fff;
    }
    .timeline-item:nth-child(odd):before
    {
        top: 20px;
        right: 39px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0); 

        border: 0;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-left: 12px solid #ccc;
    }
    .timeline-item:nth-child(odd) > .timeline-badge
    {
        top: 12px;
        right: -20px;
        left: auto;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);
    }
    .timeline-item.block:nth-child(even)
    {
        padding-bottom: 0;
    }
    .timeline-item.block:nth-child(even):after
    {
        top: 10px;
        top: 11px;
        right: 19px;
        right: 20px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);

        border: 0;
        border-top: 12px solid transparent;
        border-top: 11px solid #f5f7fa;
        border-bottom: 12px solid transparent;
        border-bottom: 11px solid #f5f7fa;
        border-left: 12px solid #ccc;
        border-left: 11px solid #fff;
    }
    .timeline-item.block:nth-child(even):before
    {
        top: 10px;
        right: 19px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0); 

        border: 0;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-left: 12px solid #ccc;
    }
    .timeline-item.block:nth-child(even) > .timeline-badge
    {
        top: 12px;
        right: -6px;
        left: auto;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);
    }
    .timeline-item.block:nth-child(odd)
    {
        padding-bottom: 0;
    }
    .timeline-item.block:nth-child(odd):after
    {
        top: 10px;
        top: 11px;
        right: 19px;
        right: 20px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);

        border: 0;
        border-top: 12px solid transparent;
        border-top: 11px solid #f5f7fa;
        border-bottom: 12px solid transparent;
        border-bottom: 11px solid #f5f7fa;
        border-left: 12px solid #ccc;
        border-left: 11px solid #fff;
    }
    .timeline-item.block:nth-child(odd):before
    {
        top: 10px;
        right: 19px;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0); 

        border: 0;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-left: 12px solid #ccc;
    }
    .timeline-item.block:nth-child(odd) > .timeline-badge
    {
        top: 12px;
        right: -6px;
        left: auto;

        -webkit-transform: translate(0, 0);
           -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
             -o-transform: translate(0, 0);
                transform: translate(0, 0);
    }
}

@media (max-width: 991.98px)
{
    #timeline.timeline-wrapper .timeline
    {
        padding: 0;
    }
    #timeline.timeline-wrapper .timeline .card-body
    {
        text-align: right;
    }
    #timeline.timeline-wrapper .timeline-line
    {
        position: absolute;
        top: 0;
        right: 50%;
        bottom: 0;

        display: inline-block;

        width: 2px;

        -webkit-transform: translate(50%, 0);
           -moz-transform: translate(50%, 0);
            -ms-transform: translate(50%, 0);
             -o-transform: translate(50%, 0);
                transform: translate(50%, 0); 

        background-color: #ddd;
    }
    #timeline.timeline-wrapper .timeline-group
    {
        display: block;

        margin-right: -6px;

        text-align: center;
    }
    #timeline.timeline-wrapper .timeline-item
    {
        display: block;

        padding: 20px 0 20px !important;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(even)
    {
        float: none;
        clear: both;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(even):after
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(even):before
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(odd)
    {
        float: none;
        clear: both;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(odd):after
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item:nth-child(odd):before
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item > .timeline-badge
    {
        top: 0 !important;
        right: 45% !important;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(even)
    {
        float: none;
        clear: both;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(even):after
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(even):before
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(odd)
    {
        float: none;
        clear: both;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(odd):after
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item.block:nth-child(odd):before
    {
        display: none;
    }
    #timeline.timeline-wrapper .timeline-item.timeline-poster > .timeline-badge
    {
        display: none;
    }
}

@media (max-width: 575.98px)
{
    .timeline-item > .timeline-badge
    {
        top: 0 !important;
        right: 44% !important;
    }
}

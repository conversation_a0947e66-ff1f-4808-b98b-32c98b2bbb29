.form-group.issue .help-block,
.form-group.issue .help-inline
{
    color: #ffa87d;
}

.form-group.issue input,
.form-group.issue select,
.form-group.issue textarea
{
    color: #ffa87d;
    border-color: #ffa87d;
}

.form-group.issue input:focus,
.form-group.issue select:focus,
.form-group.issue textarea:focus
{
    border-color: #ce2605;
    -webkit-box-shadow: 0 0 6px #fca08e;
            box-shadow: 0 0 6px #fca08e;
}

.form-group.issue .input-prepend .add-on,
.form-group.issue .input-append .add-on
{
    color: #ffa87d;
    border-color: #ffa87d; 
    background-color: #fed2c9;
}

.form-group.error .help-block,
.form-group.error .help-inline
{
    color: #ff7588;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea
{
    color: #ff7588;
    border-color: #ff7588;
}

.form-group.error input:focus,
.form-group.error select:focus,
.form-group.error textarea:focus
{
    border-color: #b41323;
    -webkit-box-shadow: 0 0 6px #f38c96;
            box-shadow: 0 0 6px #f38c96;
}

.form-group.error .input-prepend .add-on,
.form-group.error .input-append .add-on
{
    color: #ff7588;
    border-color: #ff7588; 
    background-color: #facacf;
}

.form-group.validate .help-block,
.form-group.validate .help-inline
{
    color: #16d39a;
}

.form-group.validate input,
.form-group.validate select,
.form-group.validate textarea
{
    color: #16d39a;
    border-color: #16d39a;
}

.form-group.validate input:focus,
.form-group.validate select:focus,
.form-group.validate textarea:focus
{
    border-color: #34c5a1;
    -webkit-box-shadow: 0 0 6px #78dcc3;
            box-shadow: 0 0 6px #78dcc3;
}

.form-group.validate .input-prepend .add-on,
.form-group.validate .input-append .add-on
{
    color: #16d39a;
    border-color: #16d39a; 
    background-color: #ddf6f0;
}

.form-group .help-block ul
{
    padding-right: 1.5rem;
}

<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

if(empty($_SESSION['username'])){
    header("location:../../index.php");
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

if($hak_akses == '1' || $hak_akses =='2'){ 

// Fungsi untuk format tanggal Indonesia
function tgl($tanggal){
    if(empty($tanggal) || $tanggal == '0000-00-00') return '-';
    $bulan = array(
        1 => '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    );
    $split = explode('-', $tanggal);
    return (int)$split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Dokumentasi Kegiatan - E-Doc RSK Dharmais</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="../../assets/css/main.css">
    <link rel="stylesheet" type="text/css" href="../../assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../../assets/plugins/datatables/css/dataTables.bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../../assets/css/sweetalert.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2-bootstrap-theme/0.1.0-beta.10/select2-bootstrap.min.css">
    <link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../../assets/images/favicon.ico" type="image/x-icon">
    <style>
        .modal-lg { max-width: 90%; }
        .preview-image { max-width: 100px; max-height: 100px; margin: 5px; }
        .video-input-group { margin-bottom: 10px; }
        .btn-remove-video { margin-left: 10px; }
    </style>
</head>
<body class="sidebar-mini fixed">
    <div class="wrapper">
        <header class="main-header hidden-print">
            <a class="logo" href="../index.php" style="font-size:13pt">RSK Dharmais</a>
            <nav class="navbar navbar-static-top">
                <a class="sidebar-toggle" href="#" data-toggle="offcanvas"></a>
                <div class="navbar-custom-menu">
                    <ul class="top-nav">
                        <li class="dropdown">
                            <a class="dropdown-toggle" href="#" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-user fa-lg"></i>
                            </a>
                            <ul class="dropdown-menu settings-menu">
                                <li><a href="../logout.php"><i class="fa fa-sign-out fa-lg"></i> Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>
        
        <aside class="main-sidebar hidden-print">
            <section class="sidebar">
                <div class="user-panel">
                    <center>
                        <img class="img-responsive" src="../../assets/images/logo.png" alt="Logo" width="250px">
                        <?php
                        $qpersonal = mysqli_query($koneksi,"SELECT FULL_NAME FROM edoc_admin WHERE USERNAME='$user'");
                        $datapersonal = mysqli_fetch_array($qpersonal);
                        $fullname = $datapersonal['FULL_NAME'];
                        $qunit = mysqli_query($koneksi,"SELECT UNIT FROM unit WHERE ID='$user'");
                        $dataunit = mysqli_fetch_array($qunit);
                        ?>
                        <?php if ($hak_akses == '1') { ?>
                            <p style="font-size:14pt;color:white;"><b><?php echo $fullname; ?></b><br>
                            <span>Full Administator E-Doc</span> </p>
                        <?php }elseif ($hak_akses == '2') { ?>
                            <p style="font-size:12pt;color:white;"><b>Administrator E-Doc</b><br>
                            <span style="font-size:6pt"><?php echo $dataunit['UNIT'];?></span> </p>
                        <?php } ?>
                    </center>
                </div>
                
                <ul class="sidebar-menu">
                    <li><a href='../index.php'><i class='fa fa-dashboard'></i><span>Dashboard</span></a></li>
                    <?php if ($hak_akses == '1'){ ?>
                    <li><a href='../kategori-admin.php'><i class='fa fa-list-alt'></i><span>Data Kategori Unit Kerja</span></a></li>
                    <li><a href='../unit-kerja.php'><i class='fa fa-list-alt'></i><span>Data Unit Kerja</span></a></li>
                    <li><a href='../kumpulan_sk/index.php'><i class='fa fa-list-alt'></i><span>Data Kumpulan SK</span></a></li>
                    <li><a href='../dokumen_nonaktif.php'><i class='fa fa-list-alt'></i><span>Dokumen Kadaluwarsa</span><br>
                    <span style="margin-left: 26px;">(Arsip Inaktif)</span></a></li>
                    <?php } ?>
                    <?php if ($hak_akses == '2'){ ?>
                        <li><a href='../kategori.php'><i class='fa fa-list-alt'></i><span>Data Kategori</span></a></li>
                        <li><a href='../dokumen.php'><i class='fa fa-list-alt'></i><span>Data Dokumen</span></a></li>
                        <li class="active"><a href='index.php'><i class='fa fa-camera'></i><span>Dokumentasi Kegiatan</span></a></li>
                    <?php } ?>
                    <?php if ($hak_akses == '1'){ ?>
                    <li class="active"><a href='index.php'><i class='fa fa-camera'></i><span>Dokumentasi Kegiatan</span></a></li>
                    <li><a href='../berkas-file.php'><i class='fa fa-folder-open'></i><span>Berkas File</span></a></li>
                    <li><a href='../laporan.php'><i class='fa fa-list-alt'></i><span>Laporan</span></a></li>
                    <?php } ?>
                </ul>
            </section>
        </aside>
        
        <div class="content-wrapper">
            <div class="page-title">
                <div>
                    <h1><i class="fa fa-camera"></i> Dokumentasi Kegiatan</h1>
                </div>
                <div>
                    <ul class="breadcrumb">
                        <li><i class="fa fa-home fa-lg"></i></li>
                        <li><a href="../index.php">Dashboard</a></li>
                        <li>Dokumentasi Kegiatan</li>
                    </ul>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <button class="btn btn-primary" data-toggle="modal" data-target="#modalTambahDokumentasi">
                                <i class="fa fa-plus"></i> Tambah Dokumentasi
                            </button>
                            <hr>
                            
                            <div class="table-responsive">
                                <table id="tableDokumentasi" class="table table-striped table-bordered" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Tanggal</th>
                                            <th>Judul</th>
                                            <th>Kategori</th>
                                            <th>Lokasi</th>
                                            <th>Unit</th>
                                            <th>Jenis Dokumentasi</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tambah Dokumentasi -->
    <div class="modal fade" id="modalTambahDokumentasi" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Tambah Dokumentasi Kegiatan</h4>
                </div>
                <form id="formTambahDokumentasi" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Judul Dokumentasi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="judul">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Kategori Dokumentasi <span class="text-danger">*</span></label>
                                    <select class="form-control" name="id_kategori">
                                        <option value="">Pilih Kategori</option>
                                        <?php
                                        $qkategori = mysqli_query($koneksi, "SELECT * FROM edoc_refrensi WHERE JENIS = 1 AND STATUS = 1 ORDER BY ID");
                                        while($kategori = mysqli_fetch_array($qkategori)) {
                                            echo "<option value='".$kategori['ID']."'>".$kategori['DESKRIPSI']."</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Tanggal Kegiatan <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="tanggal">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Lokasi Kegiatan </span></label>
                                    <input type="text" class="form-control" name="lokasi">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Unit Penyelenggara </span></label>
                                    <?php if ($hak_akses == '1') { ?>
                                        <select class="form-control" name="unit">
                                            <option value="">Pilih Unit</option>
                                            <?php
                                            $qunit = mysqli_query($koneksi, "SELECT * FROM unit ORDER BY UNIT");
                                            while($unit = mysqli_fetch_array($qunit)) {
                                                echo "<option value='".$unit['ID']."'>".$unit['UNIT']."</option>";
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <input type="text" class="form-control" value="<?php echo $dataunit['UNIT']; ?>" readonly>
                                        <input type="hidden" name="unit" value="<?php echo $user; ?>">
                                    <?php } ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Jenis Dokumentasi </span></label>
                                    <select class="form-control" name="id_jenis_dok">
                                        <option value="">Pilih Jenis</option>
                                        <?php
                                        $qjenis = mysqli_query($koneksi, "SELECT * FROM edoc_refrensi WHERE JENIS = 2 AND STATUS = 1 ORDER BY ID");
                                        while($jenis = mysqli_fetch_array($qjenis)) {
                                            echo "<option value='".$jenis['ID']."'>".$jenis['DESKRIPSI']."</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Upload Foto</label>
                            <input type="file" class="form-control" name="foto[]" multiple accept="image/*" id="inputFoto">
                            <small class="text-muted">Dapat memilih beberapa foto sekaligus</small>
                            <div id="previewFoto" class="mt-2"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Link Video</label>
                            <div id="videoInputs">
                                <div class="video-input-group">
                                    <input type="url" class="form-control" name="video[]" placeholder="https://youtube.com/watch?v=...">
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-success" id="btnTambahVideo">
                                <i class="fa fa-plus"></i> Tambah Video
                            </button>
                        </div>
                        
                        <div class="form-group">
                            <label>Deskripsi</label>
                            <textarea class="form-control" name="keterangan" rows="4"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Detail Dokumentasi -->
    <div class="modal fade" id="modalDetailDokumentasi" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title" id="detailModalTitle">Detail Dokumentasi</h4>
                </div>
                <div class="modal-body">
                    <div id="detailContent">
                        <!-- Content akan diisi via JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Preview Gambar -->
    <div class="modal fade" id="modalPreviewGambar" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Preview Gambar</h4>
                </div>
                <div class="modal-body text-center">
                    <img id="previewImage" src="" class="img-responsive" style="max-width: 100%; height: auto;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Edit Dokumentasi -->
    <div class="modal fade" id="modalEditDokumentasi" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Edit Dokumentasi Kegiatan</h4>
                </div>
                <form id="formEditDokumentasi" enctype="multipart/form-data">
                    <input type="hidden" name="id" id="editId">
                    <div class="modal-body">
                        <!-- Form Data Utama -->
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h5 class="panel-title">Data Utama</h5>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Judul Dokumentasi <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="judul" id="editJudul">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Kategori Dokumentasi <span class="text-danger">*</span></label>
                                            <select class="form-control" name="id_kategori" id="editKategori">
                                                <option value="">Pilih Kategori</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Tanggal Kegiatan <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="tanggal" id="editTanggal">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Lokasi Kegiatan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="lokasi" id="editLokasi">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Unit Penyelenggara <span class="text-danger">*</span></label>
                                            <div id="editUnitContainer">
                                                <!-- Will be filled by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Jenis Dokumentasi <span class="text-danger">*</span></label>
                                            <select class="form-control" name="id_jenis_dok" id="editJenisDok">
                                                <option value="">Pilih Jenis</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Deskripsi</label>
                                    <textarea class="form-control" name="keterangan" id="editKeterangan" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Tambah File Baru -->
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h5 class="panel-title">Tambah File Baru</h5>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label>Upload Foto Baru</label>
                                    <input type="file" class="form-control" name="foto_baru[]" multiple accept="image/*" id="editInputFoto">
                                    <small class="text-muted">Dapat memilih beberapa foto sekaligus</small>
                                    <div id="editPreviewFoto" class="mt-2"></div>
                                </div>

                                <div class="form-group">
                                    <label>Link Video Baru</label>
                                    <div id="editVideoInputs">
                                        <div class="video-input-group">
                                            <input type="url" class="form-control" name="video_baru[]" placeholder="https://youtube.com/watch?v=...">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" id="btnTambahVideoEdit">
                                        <i class="fa fa-plus"></i> Tambah Video
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- File Existing -->
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h5 class="panel-title">File Existing</h5>
                            </div>
                            <div class="panel-body">
                                <div id="editFileList">
                                    <!-- Will be filled by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../assets/js/jquery-2.1.4.min.js"></script>
    <script src="../../assets/js/jquery-ui.js"></script>
    <script src="../../assets/js/essential-plugins.js"></script>
    <script src="../../assets/bootstrap/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="../../assets/plugins/datatables/js/jquery.dataTables.js"></script>
    <script src="../../assets/plugins/datatables/js/dataTables.bootstrap.min.js"></script>
    <script src="../../assets/js/pace.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        // Variabel global untuk hak akses
        var hakAkses = '<?php echo $hak_akses; ?>';
        var currentUser = '<?php echo $user; ?>';

        // Initialize Select2
        $(document).ready(function() {
            // Add select2 to all form selects
            $('select[name="id_kategori"]').select2({
                theme: 'bootstrap',
                placeholder: 'Pilih Kategori',
                allowClear: true,
                width: '100%'
            });

            $('select[name="unit"]').select2({
                theme: 'bootstrap',
                placeholder: 'Pilih Unit',
                allowClear: true,
                width: '100%'
            });

            $('select[name="id_jenis_dok"]').select2({
                theme: 'bootstrap',
                placeholder: 'Pilih Jenis',
                allowClear: true,
                width: '100%'
            });

            // Fix select2 inside bootstrap modal
            $.fn.modal.Constructor.prototype.enforceFocus = function() {};

            // Reinitialize select2 when edit modal opens
            $('#modalEditDokumentasi').on('shown.bs.modal', function () {
                $('#editKategori').select2({
                    theme: 'bootstrap',
                    placeholder: 'Pilih Kategori',
                    allowClear: true,
                    width: '100%'
                });

                $('#editJenisDok').select2({
                    theme: 'bootstrap',
                    placeholder: 'Pilih Jenis',
                    allowClear: true,
                    width: '100%'
                });
            });
        });
    </script>
    <script src="js/dokumentasi.js"></script>
</body>
</html>
<?php } else { ?>
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <title>Notice!</title>
    <link rel="stylesheet" type="text/css" href="../../assets/css/sweetalert.css">
    <script src="../../assets/js/sweetalert.min.js"></script>
</head>
<body></body>
</html>
<script>
    setTimeout(function() {
        swal({
            title: 'Notice',
            text: 'Anda tidak memiliki Akses!',
            type: 'error',
            timer: 2000,
            showConfirmButton: false
        }, function() {
            window.location = '../../index.php';
        });
    });
</script>
<?php } ?>

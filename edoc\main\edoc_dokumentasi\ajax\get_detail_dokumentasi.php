<?php
session_start();
include '../../../config.php';

// Cek session
if(empty($_SESSION['username'])){
    echo json_encode(['status' => 'error', 'message' => 'Session expired']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Fungsi untuk format tanggal Indonesia
function tgl($tanggal){
    if(empty($tanggal) || $tanggal == '0000-00-00') return '-';
    $bulan = array(
        1 => 'Januari', '<PERSON><PERSON><PERSON>', 'Maret', 'April', '<PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON>', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    );
    $split = explode('-', $tanggal);
    return (int)$split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}

try {
    if (empty($_POST['id'])) {
        throw new Exception('ID dokumentasi tidak ditemukan');
    }

    $id = intval($_POST['id']);

    // Kondisi hak akses
    $whereAccess = "";
    if ($hak_akses == '2') {
        $whereAccess = " AND ed.UNIT = '$user'";
    }

    // Query data utama dokumentasi
    $sqlMain = "SELECT 
                    ed.*, 
                    er1.DESKRIPSI as KATEGORI,
                    er2.DESKRIPSI as JENIS_DOK,
                    u.UNIT as NAMA_UNIT
                FROM edoc_dokumentasi ed
                LEFT JOIN edoc_refrensi er1 ON ed.ID_KATEGORI = er1.ID
                LEFT JOIN edoc_refrensi er2 ON ed.ID_JENIS_DOK = er2.ID
                LEFT JOIN unit u ON ed.UNIT = u.ID
                WHERE ed.ID = $id AND ed.STATUS = 1 $whereAccess";

    $resultMain = mysqli_query($koneksi, $sqlMain);
    
    if (!$resultMain || mysqli_num_rows($resultMain) == 0) {
        throw new Exception('Data dokumentasi tidak ditemukan');
    }

    $dataMain = mysqli_fetch_array($resultMain);

    // Query data detail file
    $sqlDetail = "SELECT * FROM edoc_dokumentasi_detail 
                  WHERE ID_DOKUMENTASI = $id AND STATUS = 1 
                  ORDER BY TIPE_FILE, UPLOADED_AT";

    $resultDetail = mysqli_query($koneksi, $sqlDetail);
    
    $files = [];
    while ($file = mysqli_fetch_array($resultDetail)) {
        $files[] = [
            'id' => $file['ID'],
            'file_path' => $file['FILE_PATH'],
            'tipe_file' => $file['TIPE_FILE'],
            'lokasi_file' => $file['LOKASI_FILE'],
            'uploaded_at' => $file['UPLOADED_AT']
        ];
    }

    $response = [
        'status' => 'success',
        'data' => [
            'main' => [
                'id' => $dataMain['ID'],
                'judul' => $dataMain['JUDUL'],
                'kategori' => $dataMain['KATEGORI'],
                'tanggal' => tgl($dataMain['TANGGAL']),
                'tanggal_raw' => $dataMain['TANGGAL'],
                'lokasi' => $dataMain['LOKASI'],
                'unit' => $dataMain['NAMA_UNIT'],
                'jenis_dok' => $dataMain['JENIS_DOK'],
                'keterangan' => $dataMain['KETERANGAN'],
                'FILE_PATH' => $dataMain['FILE_PATH'],
                'created_at' => $dataMain['CREATED_AT']
            ],
            'files' => $files
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>

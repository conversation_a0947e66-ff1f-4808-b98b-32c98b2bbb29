# API Documentation - Dokumentasi Kegiatan

## Overview
API endpoints untuk sistem Dokumentasi Kegiatan E-Doc RSK Dharmais.

## Authentication
Semua endpoint memerlukan session yang valid dengan `$_SESSION['username']` dan `$_SESSION['hak_akses']`.

## Endpoints

### 1. Get Dokumentasi List (DataTables)
**URL:** `ajax/dokumentasi_serverside.php`  
**Method:** POST  
**Content-Type:** application/x-www-form-urlencoded

**Parameters:**
```
draw: integer (DataTables draw counter)
start: integer (Starting record number)
length: integer (Number of records to return)
search[value]: string (Search term)
order[0][column]: integer (Column index to sort)
order[0][dir]: string (asc|desc)
```

**Response:**
```json
{
    "draw": 1,
    "recordsTotal": 100,
    "recordsFiltered": 50,
    "data": [
        [
            1,                              // No
            "15 Januari 2024",             // Tanggal
            "Pelatihan Keselamatan Kerja", // Ju<PERSON>l
            "Kegiatan Pelatihan",          // <PERSON>gori
            "Aula Utama",                  // Lokasi
            "Unit Keselamatan",            // Unit
            "Dokumentasi Campuran",        // Jenis
            "<button>...</button>"         // Aksi
        ]
    ]
}
```

### 2. Get Detail Dokumentasi
**URL:** `ajax/get_detail_dokumentasi.php`  
**Method:** POST  
**Content-Type:** application/x-www-form-urlencoded

**Parameters:**
```
id: integer (ID dokumentasi)
```

**Response:**
```json
{
    "status": "success",
    "data": {
        "main": {
            "id": 1,
            "judul": "Pelatihan Keselamatan Kerja",
            "kategori": "Kegiatan Pelatihan",
            "tanggal": "15 Januari 2024",
            "tanggal_raw": "2024-01-15",
            "lokasi": "Aula Utama",
            "unit": "Unit Keselamatan",
            "jenis_dok": "Dokumentasi Campuran",
            "keterangan": "Deskripsi kegiatan...",
            "FILE_PATH": "/file_dokumentasi/...",
            "created_at": "2024-01-15 10:00:00"
        },
        "files": [
            {
                "id": 1,
                "file_path": "foto_kegiatan_1.jpg",
                "tipe_file": "foto",
                "lokasi_file": "/file_dokumentasi/...",
                "uploaded_at": "2024-01-15 10:00:00"
            }
        ]
    }
}
```

### 3. Get Dokumentasi for Edit
**URL:** `ajax/get_dokumentasi.php`  
**Method:** POST  
**Content-Type:** application/x-www-form-urlencoded

**Parameters:**
```
id: integer (ID dokumentasi)
```

**Response:**
```json
{
    "status": "success",
    "data": {
        "main": {
            "id": 1,
            "judul": "Pelatihan Keselamatan Kerja",
            "id_kategori": 1,
            "tanggal": "2024-01-15",
            "lokasi": "Aula Utama",
            "unit": "5001",
            "nama_unit": "Unit Keselamatan",
            "id_jenis_dok": 8,
            "keterangan": "Deskripsi..."
        },
        "files": [...],
        "options": {
            "kategori": [
                {"id": 1, "deskripsi": "Kegiatan Pelatihan"}
            ],
            "jenis": [
                {"id": 6, "deskripsi": "Dokumentasi Foto"}
            ],
            "unit": [
                {"id": "5001", "unit": "Unit Keselamatan"}
            ]
        }
    }
}
```

### 4. Save Dokumentasi
**URL:** `process/simpan_dokumentasi.php`  
**Method:** POST  
**Content-Type:** multipart/form-data

**Parameters:**
```
judul: string (required)
id_kategori: integer (required)
tanggal: date (required)
lokasi: string (required)
unit: string (required)
id_jenis_dok: integer (required)
keterangan: text (optional)
foto[]: file[] (optional, multiple files)
video[]: url[] (optional, multiple URLs)
```

**Response:**
```json
{
    "status": "success",
    "message": "Dokumentasi berhasil disimpan dengan 3 file"
}
```

### 5. Update Dokumentasi
**URL:** `process/update_dokumentasi.php`  
**Method:** POST  
**Content-Type:** multipart/form-data

**Parameters:**
```
id: integer (required)
judul: string (required)
id_kategori: integer (required)
tanggal: date (required)
lokasi: string (required)
unit: string (required)
id_jenis_dok: integer (required)
keterangan: text (optional)
foto_baru[]: file[] (optional)
video_baru[]: url[] (optional)
```

**Response:**
```json
{
    "status": "success",
    "message": "Dokumentasi berhasil diupdate dengan 2 file baru"
}
```

### 6. Delete Dokumentasi
**URL:** `process/hapus_dokumentasi.php`  
**Method:** POST  
**Content-Type:** application/x-www-form-urlencoded

**Parameters:**
```
id: integer (required)
```

**Response:**
```json
{
    "status": "success",
    "message": "Dokumentasi berhasil dihapus"
}
```

### 7. Delete File
**URL:** `process/hapus_file.php`  
**Method:** POST  
**Content-Type:** application/x-www-form-urlencoded

**Parameters:**
```
id: integer (required, ID file detail)
```

**Response:**
```json
{
    "status": "success",
    "message": "File berhasil dihapus"
}
```

### 8. Download Image
**URL:** `process/unduh_gambar.php`  
**Method:** GET

**Parameters:**
```
id: integer (required, ID file detail)
watermark: integer (optional, 1=with watermark, 0=original)
```

**Response:**
Binary image file with appropriate headers for download.

## Error Responses
All endpoints return error responses in this format:
```json
{
    "status": "error",
    "message": "Error description"
}
```

## Access Control
- **Admin (hak_akses = 1)**: Full access to all data
- **Unit User (hak_akses = 2)**: Access only to own unit data

## File Upload Constraints
- **Image Types:** JPEG, PNG, GIF
- **Max File Size:** 5MB per file
- **Multiple Upload:** Supported
- **Validation:** Server-side validation for file type and size

## Database Tables
- `edoc_dokumentasi`: Main documentation records
- `edoc_dokumentasi_detail`: File details (photos/videos)
- `edoc_refrensi`: Reference data (categories, types)
- `unit`: Unit/department data
- `edoc_admin`: Admin user data

## Security Notes
- All inputs are sanitized using `mysqli_real_escape_string()`
- File uploads are validated for type and size
- Session-based authentication required
- Soft delete implementation (STATUS = 0)
- Access control based on user role and unit
